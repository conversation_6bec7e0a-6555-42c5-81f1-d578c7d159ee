from config.settings_base import *

# 生产环境特定设置
DEBUG = False
ALLOWED_HOSTS = os.getenv('ALLOWED_HOSTS', '').split(',')

# 安全设置
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'

# HTTPS安全设置
SECURE_SSL_REDIRECT = True  # 强制重定向到HTTPS
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')  # 信任nginx的X-Forwarded-Proto头
SECURE_HSTS_SECONDS = 31536000  # HSTS设置为1年
SECURE_HSTS_INCLUDE_SUBDOMAINS = True  # HSTS包含子域名
SECURE_HSTS_PRELOAD = True  # 允许HSTS预加载
SECURE_CONTENT_TYPE_NOSNIFF = True  # 防止MIME类型嗅探
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'  # 引用策略
SESSION_COOKIE_SECURE = True  # 仅通过HTTPS发送会话cookie
CSRF_COOKIE_SECURE = True  # 仅通过HTTPS发送CSRF cookie

# 生产环境日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'logs' / 'django.log',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['file'],
        'level': 'INFO',
    },
}

# 确保日志目录存在
import os
os.makedirs(BASE_DIR / 'logs', exist_ok=True)

# 生产环境静态文件配置
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'
