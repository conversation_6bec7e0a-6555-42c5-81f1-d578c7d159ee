#!/bin/bash

# 生产环境部署脚本

echo "开始部署 Pecco 宠物用品官网..."

# 检查环境变量文件（优先使用 config/env.prod）
ENV_FILE=${ENV_FILE:-config/env.prod}
if [ ! -f "$ENV_FILE" ]; then
    echo "错误: 找不到 $ENV_FILE 文件，请先配置生产环境变量"
    exit 1
fi

# 加载环境变量
set -a
. "$ENV_FILE"
set +a

# 拉取最新代码（主要为了更新 nginx.conf 等宿主机文件以及构建源代码）
echo "拉取最新代码 (main 分支)..."
BRANCH=${GIT_BRANCH:-main}
REPO_DISPLAY=${GIT_REPO:-"(本地仓库)"}
echo "将从 $REPO_DISPLAY 的 $BRANCH 分支构建"

# 处理本地修改冲突
echo "检查本地修改..."
if ! git diff-index --quiet HEAD --; then
    echo "发现本地修改，暂存本地更改..."
    git stash push -m "deploy-$(date +%Y%m%d_%H%M%S)"
fi

# 拉取最新代码，设置超时
echo "拉取远程代码..."
timeout 60 git pull origin "$BRANCH" || {
    echo "警告: git pull 超时或失败，使用本地代码继续部署"
}

# 复制环境变量文件到项目根目录
echo "复制环境变量文件..."
cp "$ENV_FILE" .env

# 确保必要的目录存在
echo "创建必要的目录..."
mkdir -p pecco_backend/staticfiles
mkdir -p pecco_backend/media
mkdir -p pecco_backend/logs
mkdir -p ssl-certs
mkdir -p certbot-webroot

# 停止现有容器
echo "停止现有容器..."
docker-compose -f docker-compose.prod.yml down

# 温和清理（避免卡住）
echo "清理未使用的容器和网络..."
docker container prune -f
docker network prune -f

# 只删除 pecco 相关的镜像
echo "删除旧的 pecco 镜像..."
OLD_IMAGES=$(docker images | grep -E "(pecco|<none>)" | awk '{print $3}' | head -5)
if [ ! -z "$OLD_IMAGES" ]; then
    echo "$OLD_IMAGES" | xargs docker rmi -f 2>/dev/null || true
fi

# 重新构建并启动
set -e

echo "展开 docker-compose 配置，确认构建上下文..."
docker-compose -f docker-compose.prod.yml config > /dev/null || { echo "docker-compose 配置错误"; exit 1; }

echo "重新构建并启动容器..."
BUILD_DATE=$(date +%Y%m%d_%H%M%S)
echo "构建时间戳: $BUILD_DATE"

# 设置构建超时，避免卡住
echo "开始构建 web 服务..."
timeout 600 docker-compose -f docker-compose.prod.yml build --no-cache --build-arg BUILD_DATE=$BUILD_DATE web || {
    echo "构建超时，尝试不使用缓存重新构建..."
    docker-compose -f docker-compose.prod.yml build --build-arg BUILD_DATE=$BUILD_DATE web
}

echo "启动服务..."
docker-compose -f docker-compose.prod.yml up -d

# 等待数据库启动
echo "等待数据库启动..."
sleep 30

# 执行数据库迁移
echo "执行数据库迁移..."
docker-compose -f docker-compose.prod.yml exec web python manage.py migrate

# 收集静态文件
echo "收集静态文件..."
docker-compose -f docker-compose.prod.yml exec web python manage.py collectstatic --noinput --clear --verbosity=0

# 检查SSL证书
check_ssl_certificate() {
    if [ -f "./ssl-certs/fullchain.pem" ] && [ -f "./ssl-certs/privkey.pem" ]; then
        echo "SSL证书已存在，跳过证书获取"
        return 0
    else
        echo "未找到SSL证书，请运行 setup-ssl.sh 获取证书"
        echo "或者手动将证书文件放置在 ssl-certs/ 目录下"
        return 1
    fi
}

echo "检查SSL证书..."
if check_ssl_certificate; then
    echo "SSL证书检查通过"
else
    echo "警告: 未找到SSL证书，HTTPS功能可能无法正常工作"
fi

echo "部署完成！"
echo "HTTP访问地址: http://124.220.186.165"
if [ -f "./ssl-certs/fullchain.pem" ]; then
    echo "HTTPS访问地址: https://pecco.pet"
    echo "HTTPS访问地址: https://www.pecco.pet"
else
    echo "要启用HTTPS，请运行: sudo ./setup-ssl.sh"
fi
