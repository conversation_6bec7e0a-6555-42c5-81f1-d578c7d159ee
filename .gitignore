# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
# 只忽略collectstatic生成的目录，不忽略源文件
# 注意：staticfiles/目录会在部署时通过collectstatic命令重新生成
staticfiles/
# 忽略旧的静态文件目录（如果存在）
pecco_backend/static/admin/
pecco_backend/static/rest_framework/
pecco_backend/static/ckeditor/
pecco_backend/static/django_ckeditor_5/
# 运行时生成的日志目录
logs/

# 环境变量文件
.env
.env.local
.env.prod
config/env.prod
config/env.local

# 虚拟环境
venv/
.venv/
ENV/
env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# 临时文件
*.tmp
*.temp
