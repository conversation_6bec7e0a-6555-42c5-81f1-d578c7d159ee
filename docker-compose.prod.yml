version: '3.8'

services:
  db:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: ${MYSQL_DATABASE:-pecco}
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  web:
    build:
      context: .
      dockerfile: Dockerfile.prod
    expose:
      - "8000"
    depends_on:
      db:
        condition: service_healthy
    environment:
      - DJANGO_ENV=${DJANGO_ENV:-prod}
      - SECRET_KEY=${SECRET_KEY}
      - DEBUG=${DEBUG:-False}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS}
      - MYSQL_HOST=db
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=${MYSQL_DATABASE:-pecco}
      - MYSQL_USER=root
      - MYSQL_PASSWORD=${MYSQL_ROOT_PASSWORD}
    volumes:
      - ./pecco_backend/media:/app/pecco_backend/media
      - ./pecco_backend/staticfiles:/app/pecco_backend/staticfiles
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - ./pecco_backend/staticfiles:/app/pecco_backend/staticfiles
      - ./pecco_backend/media:/app/pecco_backend/media
      - ./ssl-certs:/etc/nginx/ssl:ro
      - ./certbot-webroot:/var/www/certbot:ro
    depends_on:
      - web
    restart: unless-stopped

  certbot:
    image: certbot/certbot
    volumes:
      - ./ssl-certs:/etc/letsencrypt
      - ./certbot-webroot:/var/www/certbot
    command: echo "Certbot container for SSL certificate management"
    profiles:
      - tools

volumes:
  mysql_data:
