#!/bin/bash

# 部署状态检查脚本

echo "=== Pecco 宠物用品官网部署状态检查 ==="
echo

# 检查容器状态
echo "1. 检查容器状态:"
docker-compose -f docker-compose.prod.yml ps
echo

# 检查最新的镜像构建时间
echo "2. 检查镜像信息:"
docker images | grep -E "(pecco|REPOSITORY)" | head -5
echo

# 检查 web 容器的构建时间戳
echo "3. 检查 web 容器的构建时间戳:"
docker-compose -f docker-compose.prod.yml exec web printenv BUILD_DATE 2>/dev/null || echo "未找到构建时间戳"
echo

# 检查最新的代码提交
echo "4. 检查本地代码状态:"
echo "当前分支: $(git branch --show-current)"
echo "最新提交: $(git log -1 --oneline)"
echo "远程状态: $(git status -uno)"
echo

# 检查容器内的代码版本（通过检查某个最近修改的文件）
echo "5. 检查容器内代码是否最新:"
if docker-compose -f docker-compose.prod.yml exec web test -f /app/pecco_backend/pecco_site/management/commands/enhance_variants.py; then
    echo "✅ 容器内包含最新的 enhance_variants.py 文件"
else
    echo "❌ 容器内缺少最新的 enhance_variants.py 文件"
fi

# 检查容器内是否还有旧的命令文件
echo "6. 检查容器内是否清理了旧文件:"
OLD_FILES=$(docker-compose -f docker-compose.prod.yml exec web find /app/pecco_backend/pecco_site/management/commands -name "init_*.py" 2>/dev/null | wc -l)
if [ "$OLD_FILES" -eq 0 ]; then
    echo "✅ 旧的初始化文件已清理"
else
    echo "❌ 容器内仍有 $OLD_FILES 个旧的初始化文件"
fi
echo

# 检查网站是否可访问
echo "7. 检查网站访问状态:"
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://124.220.186.165 || echo "连接失败")
if [ "$HTTP_STATUS" = "200" ]; then
    echo "✅ 网站可正常访问 (HTTP $HTTP_STATUS)"
else
    echo "❌ 网站访问异常 (HTTP $HTTP_STATUS)"
fi
echo

# 检查容器日志（最后10行）
echo "8. 检查容器日志（最后10行）:"
echo "--- Web 容器日志 ---"
docker-compose -f docker-compose.prod.yml logs --tail=10 web
echo

echo "=== 检查完成 ==="
