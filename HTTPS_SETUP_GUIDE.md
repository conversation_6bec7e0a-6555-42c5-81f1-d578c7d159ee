# HTTPS配置指南

本指南将帮助您为Pecco宠物用品网站配置HTTPS访问，使用Let's Encrypt免费SSL证书。

## 前提条件

1. **域名解析**: 确保您的域名（如 `pecco.pet` 和 `www.pecco.pet`）已正确解析到服务器IP地址
2. **防火墙设置**: 确保服务器的80和443端口已开放
3. **Root权限**: 需要root权限来安装certbot和获取证书

## 快速配置步骤

### 1. 设置环境变量

在运行SSL配置脚本之前，请设置必要的环境变量：

```bash
# 设置您的域名
export DOMAIN=pecco.pet

# 设置您的邮箱（用于Let's Encrypt通知）
export EMAIL=<EMAIL>
```

### 2. 运行SSL配置脚本

```bash
# 给脚本执行权限
chmod +x setup-ssl.sh

# 运行SSL配置脚本（需要root权限）
sudo ./setup-ssl.sh
```

### 3. 验证HTTPS配置

配置完成后，您可以通过以下方式验证：

```bash
# 测试HTTP重定向
curl -I http://pecco.pet

# 测试HTTPS连接
curl -I https://pecco.pet

# 检查SSL证书信息
openssl s_client -connect pecco.pet:443 -servername pecco.pet
```

## 自动续期设置

SSL证书有效期为90天，需要定期续期。我们提供了自动续期脚本。

### 1. 配置续期脚本

编辑 `renew-ssl.sh` 文件，修改以下变量：

```bash
# 修改为实际的项目路径
PROJECT_DIR="/path/to/pecco-pet-shop"

# 修改为实际的域名
DOMAIN="pecco.pet"
```

### 2. 设置定时任务

```bash
# 给续期脚本执行权限
chmod +x renew-ssl.sh

# 编辑crontab
sudo crontab -e

# 添加以下行（每天凌晨2点检查证书）
0 2 * * * /path/to/pecco-pet-shop/renew-ssl.sh >> /var/log/ssl-renewal.log 2>&1
```

## 文件说明

### 新增文件

- `setup-ssl.sh`: SSL证书获取和初始配置脚本
- `renew-ssl.sh`: SSL证书自动续期脚本
- `ssl-certs/`: SSL证书存储目录
- `certbot-webroot/`: Certbot验证文件目录

### 修改的文件

- `nginx.conf`: 添加了HTTPS配置和HTTP重定向
- `docker-compose.prod.yml`: 添加了443端口映射和SSL证书卷挂载
- `deploy.sh`: 集成了SSL证书检查功能

## 配置详解

### Nginx配置

新的nginx配置包含两个server块：

1. **HTTP服务器** (端口80):
   - 处理Let's Encrypt验证请求
   - 将其他所有HTTP请求重定向到HTTPS

2. **HTTPS服务器** (端口443):
   - 提供SSL/TLS加密
   - 包含安全头配置
   - 反向代理到Django应用

### SSL安全配置

- **协议**: 仅支持TLS 1.2和1.3
- **加密套件**: 使用现代安全的加密算法
- **安全头**: 包含HSTS、X-Frame-Options等安全头
- **会话缓存**: 优化SSL握手性能

## 故障排除

### 常见问题

1. **域名解析问题**
   ```bash
   # 检查域名解析
   nslookup pecco.pet
   dig pecco.pet
   ```

2. **防火墙问题**
   ```bash
   # 检查端口是否开放
   sudo ufw status
   sudo firewall-cmd --list-ports
   ```

3. **证书获取失败**
   ```bash
   # 检查certbot日志
   sudo tail -f /var/log/letsencrypt/letsencrypt.log
   ```

4. **Nginx配置错误**
   ```bash
   # 测试nginx配置
   docker-compose -f docker-compose.prod.yml exec nginx nginx -t
   ```

### 手动证书获取

如果自动脚本失败，可以手动获取证书：

```bash
# 停止nginx容器
docker-compose -f docker-compose.prod.yml stop nginx

# 手动获取证书
sudo certbot certonly --standalone -d pecco.pet -d www.pecco.pet

# 复制证书到项目目录
sudo cp /etc/letsencrypt/live/pecco.pet/fullchain.pem ./ssl-certs/
sudo cp /etc/letsencrypt/live/pecco.pet/privkey.pem ./ssl-certs/

# 设置权限
sudo chmod 644 ./ssl-certs/fullchain.pem
sudo chmod 600 ./ssl-certs/privkey.pem

# 重启nginx容器
docker-compose -f docker-compose.prod.yml start nginx
```

## 安全建议

1. **定期更新**: 保持系统和Docker镜像更新
2. **监控证书**: 设置证书过期监控
3. **备份证书**: 定期备份SSL证书
4. **日志监控**: 监控SSL相关日志
5. **安全扫描**: 定期进行SSL安全扫描

## 测试工具

- [SSL Labs测试](https://www.ssllabs.com/ssltest/): 全面的SSL配置测试
- [Mozilla Observatory](https://observatory.mozilla.org/): 网站安全配置检查
- [Security Headers](https://securityheaders.com/): 安全头配置检查

配置完成后，您的网站将支持HTTPS访问，并自动将HTTP请求重定向到HTTPS，提供更安全的用户体验。
