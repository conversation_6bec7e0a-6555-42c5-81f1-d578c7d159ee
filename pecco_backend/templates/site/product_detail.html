{% extends 'site/base.html' %}
{% block content %}
<article class="product-detail">
  <div class="gallery">
    <div class="main-image-container">
      <img id="main-product-image" src="{{ product.cover }}" alt="{{ product.name }}" class="main-image cover-image">
      {% if product.hover %}
        <img src="{{ product.hover }}" alt="{{ product.name }}" class="main-image hover-image">
      {% endif %}
    </div>
    <div class="thumbnail-gallery">
      <!-- 默认产品图片：只显示 cover 和 hover -->
      {% if product.cover %}
        <img src="{{ product.cover }}" alt="{{ product.name }}" class="thumbnail active default-thumbnail" data-image="{{ product.cover }}">
      {% endif %}
      {% if product.hover %}
        <img src="{{ product.hover }}" alt="{{ product.name }}" class="thumbnail default-thumbnail" data-image="{{ product.hover }}">
      {% endif %}
      <!-- 变体图片将通过 JavaScript 动态添加 -->
    </div>
  </div>

  <div class="product-info">
    <div class="product-header">
      <h1 class="product-title">{{ product.name }}</h1>
      <p class="product-description">{{ product.short_desc }}</p>
      <div class="product-tags">
        {% for t in product.tags %}<span class="tag tag-{{ t }}">{{ t|upper }}</span>{% endfor %}
      </div>
    </div>

    <!-- 产品选项 -->
    {% if product.options %}
    <div class="product-options">
      {% for option in product.options %}
        <div class="option-group" data-option-type="{{ option.type }}">
          <h3 class="option-title">{{ option.name }}</h3>
          <div class="option-values {% if option.type == 'color' %}color-options{% else %}size-options{% endif %}">
            {% for value in option.values %}
              {% if option.type == 'color' %}
                <button class="color-option"
                        data-value-id="{{ value.id }}"
                        data-value="{{ value.original_value }}"
                        {% if value.color_code %}style="background-color: {{ value.color_code }}"{% endif %}
                        title="{{ value.value }}">
                  {% if not value.color_code %}{{ value.value }}{% endif %}
                </button>
              {% else %}
                <button class="size-option"
                        data-value-id="{{ value.id }}"
                        data-value="{{ value.original_value }}">
                  {{ value.value }}
                </button>
              {% endif %}
            {% endfor %}
          </div>
        </div>
      {% endfor %}
    </div>
    {% endif %}

    <!-- 产品详情标签页 -->
    <div class="product-details">
      <div class="detail-tabs">
        <button class="tab-button active" data-tab="details">Product Details</button>
        <button class="tab-button" data-tab="specifications">Specificaties</button>
      </div>

      <div class="tab-content">
        <div id="details-tab" class="tab-pane active">
          {% if not product.use_rich_desc_fallback %}
            <div class="rich-content">{{ product.default_product_details|safe }}</div>
          {% elif product.rich_desc %}
            <div class="rich-content">{{ product.rich_desc|safe }}</div>
          {% endif %}
        </div>

        <!-- 存储默认产品详情，用于JavaScript重置 -->
        <div data-default-product-details style="display: none;">
          {% if not product.use_rich_desc_fallback %}
            <div class="rich-content">{{ product.default_product_details|safe }}</div>
          {% elif product.rich_desc %}
            <div class="rich-content">{{ product.rich_desc|safe }}</div>
          {% endif %}
        </div>

        <div id="specifications-tab" class="tab-pane">
          <div class="specifications-content">
            <!-- 通用规格 -->
            {% if product.general_specifications %}
              <div class="spec-section">
                <h4>General Specifications</h4>
                <div class="spec-list">
                  {% for spec in product.general_specifications %}
                    <div class="spec-item">
                      <span class="spec-key">{{ spec.key }}</span>
                      <span class="spec-value">{{ spec.value }}</span>
                    </div>
                  {% endfor %}
                </div>
              </div>
            {% endif %}

            <!-- 变体特定规格 -->
            <div id="variant-specifications" class="spec-section" style="display: none;">
              <h4>Variant Specifications</h4>
              <div class="spec-list" id="variant-spec-list">
                <!-- 动态内容 -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</article>

<!-- 变体数据 (JSON) -->
<script type="application/json" id="product-variants-data">
{{ product.variants|safe }}
</script>

{% endblock %}

