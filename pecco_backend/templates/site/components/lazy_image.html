{% load image_tags %}

<div class="lazy-image-container {{ css_class }}">
    {% if image_field %}
        <img 
            class="lazy-image {{ placeholder_class }}"
            src="{% if thumbnail_url %}{{ thumbnail_url }}{% else %}data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='300'%3E%3Crect width='100%25' height='100%25' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='%23999'%3ELoading...%3C/text%3E%3C/svg%3E{% endif %}"
            data-src="{{ image_field.url }}"
            {% if medium_url and large_url %}
            data-srcset="{{ thumbnail_url }} 400w, {{ medium_url }} 800w, {{ large_url }} 1200w"
            data-sizes="(max-width: 400px) 400px, (max-width: 800px) 800px, 1200px"
            {% endif %}
            alt="{{ alt_text }}"
            loading="lazy"
        />
        <noscript>
            <img src="{{ image_field.url }}" alt="{{ alt_text }}" class="{{ css_class }}">
        </noscript>
    {% else %}
        <div class="image-placeholder {{ placeholder_class }}">
            <span>No Image</span>
        </div>
    {% endif %}
</div>
