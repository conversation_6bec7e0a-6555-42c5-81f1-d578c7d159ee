{% extends 'site/base.html' %}
{% load static %}
{% load image_tags %}

{% block title %}图片优化测试{% endblock %}

{% block content %}
<div class="container">
    <h1>图片优化测试页面</h1>
    
    <div class="row">
        <div class="col-md-12">
            <h2>产品图片对比</h2>
            {% for product in products %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>{{ product }} - 图片优化对比</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6>原图</h6>
                                {% if product.cover_image %}
                                    <img src="{{ product.cover_image.url }}" class="img-fluid" alt="原图">
                                    <p><small>大小: 原始尺寸</small></p>
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                                <h6>中等尺寸 (WebP)</h6>
                                {% if product.cover_image %}
                                    <img src="{% optimized_image_url product.cover_image 'medium' %}" class="img-fluid" alt="优化图">
                                    <p><small>大小: 800px, WebP格式</small></p>
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                                <h6>缩略图 (WebP)</h6>
                                {% if product.cover_image %}
                                    <img src="{% optimized_image_url product.cover_image 'thumbnail' %}" class="img-fluid" alt="缩略图">
                                    <p><small>大小: 400px, WebP格式</small></p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <h2>懒加载测试</h2>
            <p>向下滚动查看懒加载效果</p>
            
            {% for product in products %}
                <div style="height: 300px; margin-bottom: 50px;">
                    <h5>{{ product }}</h5>
                    {% if product.cover_image %}
                        {% lazy_image product.cover_image alt_text=product css_class="img-fluid" %}
                    {% endif %}
                </div>
            {% endfor %}
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <h2>响应式图片测试</h2>
            {% for product in products %}
                <div class="mb-4">
                    <h5>{{ product }}</h5>
                    {% if product.cover_image %}
                        {% responsive_image product.cover_image alt_text=product css_class="img-fluid responsive-image" %}
                    {% endif %}
                </div>
            {% endfor %}
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.img-fluid {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px;
}

h6 {
    color: #666;
    margin-bottom: 10px;
}

small {
    color: #999;
}
</style>
{% endblock %}
