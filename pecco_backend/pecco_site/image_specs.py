"""
图片处理规格定义
使用django-imagekit自动生成不同尺寸和质量的图片
"""
from imagekit import ImageSpec, register
from imagekit.processors import ResizeToFit, ResizeToFill
from pilkit.processors import Thumbnail


@register.generator('product_cover_thumbnail')
class ProductCoverThumbnail(ImageSpec):
    """产品封面缩略图 - 用于产品列表页"""
    processors = [ResizeToFit(400, 400)]
    format = 'WEBP'
    options = {'quality': 85}


@register.generator('product_cover_medium')
class ProductCoverMedium(ImageSpec):
    """产品封面中等尺寸 - 用于产品详情页"""
    processors = [ResizeToFit(800, 800)]
    format = 'WEBP'
    options = {'quality': 90}


@register.generator('product_cover_large')
class ProductCoverLarge(ImageSpec):
    """产品封面大尺寸 - 用于高清显示"""
    processors = [ResizeToFit(1200, 1200)]
    format = 'WEBP'
    options = {'quality': 95}


@register.generator('carousel_thumbnail')
class CarouselThumbnail(ImageSpec):
    """轮播图缩略图"""
    processors = [ResizeToFit(600, 300)]
    format = 'WEBP'
    options = {'quality': 85}


@register.generator('carousel_medium')
class CarouselMedium(ImageSpec):
    """轮播图中等尺寸"""
    processors = [ResizeToFit(1200, 600)]
    format = 'WEBP'
    options = {'quality': 90}


@register.generator('carousel_large')
class CarouselLarge(ImageSpec):
    """轮播图大尺寸"""
    processors = [ResizeToFit(1920, 960)]
    format = 'WEBP'
    options = {'quality': 95}


@register.generator('category_icon_small')
class CategoryIconSmall(ImageSpec):
    """分类图标小尺寸"""
    processors = [ResizeToFit(64, 64)]
    format = 'WEBP'
    options = {'quality': 85}


@register.generator('category_icon_medium')
class CategoryIconMedium(ImageSpec):
    """分类图标中等尺寸"""
    processors = [ResizeToFit(128, 128)]
    format = 'WEBP'
    options = {'quality': 90}


@register.generator('variant_image_thumbnail')
class VariantImageThumbnail(ImageSpec):
    """产品变体图片缩略图"""
    processors = [ResizeToFit(200, 200)]
    format = 'WEBP'
    options = {'quality': 85}


@register.generator('variant_image_medium')
class VariantImageMedium(ImageSpec):
    """产品变体图片中等尺寸"""
    processors = [ResizeToFit(600, 600)]
    format = 'WEBP'
    options = {'quality': 90}


@register.generator('variant_image_large')
class VariantImageLarge(ImageSpec):
    """产品变体图片大尺寸"""
    processors = [ResizeToFit(1000, 1000)]
    format = 'WEBP'
    options = {'quality': 95}
