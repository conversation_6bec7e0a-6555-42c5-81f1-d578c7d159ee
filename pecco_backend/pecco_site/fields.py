"""
自定义字段
包含自动压缩的ImageField
"""
from django.db import models
from django.core.files.base import ContentFile
from .image_utils import optimize_uploaded_image


class CompressedImageField(models.ImageField):
    """
    自动压缩的ImageField
    在保存时自动压缩图片
    """

    def __init__(self, *args, **kwargs):
        # 提取压缩类型参数
        self.compression_type = kwargs.pop('compression_type', 'product')
        super().__init__(*args, **kwargs)

    def save_form_data(self, instance, data):
        """
        处理表单上传的数据
        """
        if data is not None and hasattr(data, 'read'):
            try:
                # 压缩图片
                compressed_file = optimize_uploaded_image(
                    data,
                    field_type=self.compression_type
                )

                if compressed_file != data:
                    data = compressed_file

            except Exception as e:
                # 如果压缩失败，使用原文件
                pass

        super().save_form_data(instance, data)


class ProductImageField(CompressedImageField):
    """产品图片字段"""
    def __init__(self, *args, **kwargs):
        kwargs['compression_type'] = 'product'
        super().__init__(*args, **kwargs)


class CarouselImageField(CompressedImageField):
    """轮播图片字段"""
    def __init__(self, *args, **kwargs):
        kwargs['compression_type'] = 'carousel'
        super().__init__(*args, **kwargs)


class CategoryImageField(CompressedImageField):
    """分类图片字段"""
    def __init__(self, *args, **kwargs):
        kwargs['compression_type'] = 'category'
        super().__init__(*args, **kwargs)


class VariantImageField(CompressedImageField):
    """变体图片字段"""
    def __init__(self, *args, **kwargs):
        kwargs['compression_type'] = 'variant'
        super().__init__(*args, **kwargs)
