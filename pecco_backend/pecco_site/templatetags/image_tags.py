"""
图片相关的模板标签
提供响应式图片和懒加载功能
"""
from django import template
from django.utils.safestring import mark_safe
from django.utils.html import format_html

register = template.Library()


@register.simple_tag
def responsive_image(image_field, alt_text="", css_class="", lazy=True, sizes=""):
    """
    生成响应式图片标签
    
    Args:
        image_field: 图片字段对象
        alt_text: 图片alt属性
        css_class: CSS类名
        lazy: 是否启用懒加载
        sizes: 响应式尺寸规则
    """
    if not image_field:
        return ""
    
    # 检查是否有优化的图片版本
    thumbnail_url = ""
    medium_url = ""
    large_url = ""
    original_url = image_field.url
    
    # 尝试获取不同尺寸的图片
    try:
        if hasattr(image_field, 'instance'):
            instance = image_field.instance
            field_name = image_field.field.name
            
            # 根据字段名称确定优化图片的属性名
            if field_name == 'cover_image':
                thumbnail_url = getattr(instance, 'cover_thumbnail', None)
                medium_url = getattr(instance, 'cover_medium', None)
                large_url = getattr(instance, 'cover_large', None)
            elif field_name == 'hover_image':
                thumbnail_url = getattr(instance, 'hover_thumbnail', None)
                medium_url = getattr(instance, 'hover_medium', None)
                large_url = getattr(instance, 'hover_large', None)
            elif field_name == 'image':
                thumbnail_url = getattr(instance, 'image_thumbnail', None)
                medium_url = getattr(instance, 'image_medium', None)
                large_url = getattr(instance, 'image_large', None)
            elif field_name == 'icon':
                thumbnail_url = getattr(instance, 'icon_small', None)
                medium_url = getattr(instance, 'icon_medium', None)
                
            # 转换为URL
            if thumbnail_url and hasattr(thumbnail_url, 'url'):
                thumbnail_url = thumbnail_url.url
            if medium_url and hasattr(medium_url, 'url'):
                medium_url = medium_url.url
            if large_url and hasattr(large_url, 'url'):
                large_url = large_url.url
                
    except Exception:
        # 如果获取优化图片失败，使用原图
        pass
    
    # 构建srcset
    srcset_parts = []
    if thumbnail_url:
        srcset_parts.append(f"{thumbnail_url} 400w")
    if medium_url:
        srcset_parts.append(f"{medium_url} 800w")
    if large_url:
        srcset_parts.append(f"{large_url} 1200w")
    
    srcset = ", ".join(srcset_parts) if srcset_parts else ""
    
    # 默认sizes规则
    if not sizes and srcset:
        sizes = "(max-width: 400px) 400px, (max-width: 800px) 800px, 1200px"
    
    # 构建img标签属性
    img_attrs = {
        'alt': alt_text,
        'class': css_class,
    }
    
    if lazy:
        img_attrs['loading'] = 'lazy'
        img_attrs['data-src'] = original_url
        if srcset:
            img_attrs['data-srcset'] = srcset
            img_attrs['data-sizes'] = sizes
        # 使用小的占位图片
        img_attrs['src'] = thumbnail_url or original_url
    else:
        img_attrs['src'] = original_url
        if srcset:
            img_attrs['srcset'] = srcset
            img_attrs['sizes'] = sizes
    
    # 生成HTML
    attr_string = " ".join([f'{k}="{v}"' for k, v in img_attrs.items() if v])
    return format_html('<img {}>', mark_safe(attr_string))


@register.simple_tag
def optimized_image_url(image_field, size="medium"):
    """
    获取优化后的图片URL
    
    Args:
        image_field: 图片字段对象
        size: 尺寸 (thumbnail, medium, large)
    """
    if not image_field:
        return ""
    
    try:
        if hasattr(image_field, 'instance'):
            instance = image_field.instance
            field_name = image_field.field.name
            
            # 根据字段名称和尺寸确定属性名
            attr_name = ""
            if field_name == 'cover_image':
                attr_name = f'cover_{size}'
            elif field_name == 'hover_image':
                attr_name = f'hover_{size}'
            elif field_name == 'image':
                attr_name = f'image_{size}'
            elif field_name == 'icon':
                if size == 'thumbnail':
                    attr_name = 'icon_small'
                else:
                    attr_name = 'icon_medium'
            
            if attr_name:
                optimized_field = getattr(instance, attr_name, None)
                if optimized_field and hasattr(optimized_field, 'url'):
                    return optimized_field.url
                    
    except Exception:
        pass
    
    # 如果获取优化图片失败，返回原图URL
    return image_field.url if image_field else ""


@register.inclusion_tag('site/components/lazy_image.html')
def lazy_image(image_field, alt_text="", css_class="", placeholder_class="image-placeholder"):
    """
    懒加载图片组件
    """
    return {
        'image_field': image_field,
        'alt_text': alt_text,
        'css_class': css_class,
        'placeholder_class': placeholder_class,
        'thumbnail_url': optimized_image_url(image_field, 'thumbnail'),
        'medium_url': optimized_image_url(image_field, 'medium'),
        'large_url': optimized_image_url(image_field, 'large'),
    }
