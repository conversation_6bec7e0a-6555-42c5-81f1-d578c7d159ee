/**
 * 支持剪切板粘贴、拖拽和文件选择的图片上传Widget
 */

(function() {
    'use strict';

    // 等待DOM加载完成
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Pasteable Image Widget: DOM loaded, initializing...');
        initPasteableImageWidgets();
    });

    // 初始化所有的图片上传widget
    function initPasteableImageWidgets() {
        const widgets = document.querySelectorAll('.pasteable-image-widget');
        console.log('Found', widgets.length, 'pasteable image widgets');

        widgets.forEach(function(container) {
            // 防止重复初始化
            if (container.classList.contains('initialized')) {
                return;
            }
            container.classList.add('initialized');

            const fieldName = container.getAttribute('data-field-name');
            const fileInput = container.querySelector('input[type="file"]');
            const uploadArea = container.querySelector('.image-upload-area');
            const preview = container.querySelector('.image-preview');
            const previewImg = container.querySelector('.preview-img');
            const status = container.querySelector('.upload-status');
            const browseBtn = container.querySelector('.browse-btn');
            const removeBtn = container.querySelector('.remove-image-btn');
            const changeBtn = container.querySelector('.change-image-btn');

            if (!fileInput || !uploadArea) {
                console.log('Required elements not found for widget:', fieldName);
                return;
            }

            // 浏览文件按钮点击事件
            if (browseBtn) {
                browseBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    fileInput.click();
                });
            }

            // 文件选择事件
            fileInput.addEventListener('change', function() {
                const file = this.files[0];
                if (file) {
                    handleFile(file);
                }
            });

            // 拖拽事件
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                e.stopPropagation();
                uploadArea.classList.add('drag-over');
            });

            uploadArea.addEventListener('dragenter', function(e) {
                e.preventDefault();
                e.stopPropagation();
                uploadArea.classList.add('drag-over');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                e.stopPropagation();
                uploadArea.classList.remove('drag-over');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                uploadArea.classList.remove('drag-over');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    if (file.type.startsWith('image/')) {
                        handleFile(file);
                    } else {
                        showStatus('请选择图片文件', 'error');
                    }
                }
            });

            // 剪切板粘贴事件
            container.addEventListener('paste', function(e) {
                const clipboardData = e.clipboardData;
                if (clipboardData && clipboardData.items) {
                    for (let i = 0; i < clipboardData.items.length; i++) {
                        const item = clipboardData.items[i];
                        if (item.type.startsWith('image/')) {
                            e.preventDefault();
                            const file = item.getAsFile();
                            handleFile(file);
                            break;
                        }
                    }
                }
            });

            // 删除图片
            if (removeBtn) {
                removeBtn.addEventListener('click', function() {
                    removeImage();
                });
            }

            // 更换图片
            if (changeBtn) {
                changeBtn.addEventListener('click', function() {
                    fileInput.click();
                });
            }

            // 处理文件上传
            function handleFile(file) {
                if (!file.type.startsWith('image/')) {
                    showStatus('请选择图片文件', 'error');
                    return;
                }

                // 检查文件大小 (50MB限制，因为会自动压缩)
                if (file.size > 50 * 1024 * 1024) {
                    showStatus('图片文件不能超过50MB', 'error');
                    return;
                }

                showStatus('正在处理图片...', 'loading');

                // 创建FileReader来预览图片
                const reader = new FileReader();
                reader.onload = function(e) {
                    if (previewImg) {
                        previewImg.src = e.target.result;
                        preview.classList.add('show');
                        uploadArea.classList.add('has-image');
                        showStatus('图片已选择，保存后生效', 'success');
                    }
                };
                reader.readAsDataURL(file);

                // 设置文件到input
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                fileInput.files = dataTransfer.files;
            }

            // 删除图片
            function removeImage() {
                if (previewImg) {
                    previewImg.src = '';
                    preview.classList.remove('show');
                    uploadArea.classList.remove('has-image');
                    fileInput.value = '';
                    showStatus('图片已删除', 'success');
                }
            }

            // 显示状态信息
            function showStatus(message, type) {
                if (status) {
                    status.className = 'upload-status ' + type;
                    status.textContent = message;

                    if (type === 'success' || type === 'error') {
                        setTimeout(function() {
                            status.textContent = '';
                            status.className = 'upload-status';
                        }, 3000);
                    }
                }
            }
        });
    }

    // 支持动态添加的widget（如内联表单）
    if (typeof django !== 'undefined' && django.jQuery) {
        django.jQuery(document).on('formset:added', function() {
            setTimeout(initPasteableImageWidgets, 100);
        });
    }

})();
