// 产品变体规格管理辅助脚本

(function($) {
    $(document).ready(function() {
        // 添加规格说明
        addSpecificationHelp();
        
        // 为规格键添加常用选项
        addSpecKeyHelper();
        
        // 为新添加的行绑定事件
        $('.add-row a').click(function() {
            setTimeout(function() {
                addSpecKeyHelper();
            }, 100);
        });
    });
    
    function addSpecificationHelp() {
        var helpHtml = `
            <div class="specification-help">
                <h4>📋 变体规格管理说明</h4>
                <ul>
                    <li><strong>在此处维护该变体的所有规格信息</strong> - 包括重量、材质、洗涤方式等</li>
                    <li><strong>规格键</strong>：如 Weight、Material、Washing、Size 等</li>
                    <li><strong>规格值</strong>：具体的规格信息，如 "2.0 kg GREEN"、"Premium Cotton GREEN" 等</li>
                    <li><strong>颜色信息</strong>：如果该变体有颜色，请在规格值中包含颜色信息</li>
                    <li><strong>排序</strong>：数字越小越靠前显示</li>
                </ul>
            </div>
        `;
        
        $('.inline-group h2:contains("变体规格")').after(helpHtml);
    }
    
    function addSpecKeyHelper() {
        // 为规格键输入框添加常用选项
        $('input[name*="spec_key"]').each(function() {
            if (!$(this).data('helper-added')) {
                $(this).data('helper-added', true);
                
                // 添加常用规格键的数据列表
                var datalistId = 'spec-key-options-' + Math.random().toString(36).substr(2, 9);
                $(this).attr('list', datalistId);
                
                var datalist = $(`
                    <datalist id="${datalistId}">
                        <option value="Weight">重量</option>
                        <option value="Material">材质</option>
                        <option value="Washing">洗涤方式</option>
                        <option value="Size">尺寸</option>
                        <option value="Color">颜色</option>
                        <option value="Brand">品牌</option>
                        <option value="Type">类型</option>
                        <option value="Suitable for">适用对象</option>
                    </datalist>
                `);
                
                $(this).after(datalist);
                
                // 添加提示文本
                $(this).attr('placeholder', '输入规格键，如：Weight、Material 等');
            }
        });
        
        // 为规格值输入框添加提示
        $('textarea[name*="spec_value"]').each(function() {
            if (!$(this).data('helper-added')) {
                $(this).data('helper-added', true);
                $(this).attr('placeholder', '输入规格值，如：2.0 kg GREEN、Premium Cotton GREEN 等\n如果该变体有颜色，请在值中包含颜色信息');
            }
        });
    }
})(django.jQuery);
