/* 产品变体规格内联表单样式优化 */

.inline-group .tabular .form-row {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    margin-bottom: 5px;
    border-radius: 4px;
}

.inline-group .tabular .form-row:hover {
    background-color: #e9ecef;
}

/* 变体规格说明提示 */
.specification-help {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 15px;
    color: #0c5460;
}

.specification-help h4 {
    margin: 0 0 8px 0;
    color: #0c5460;
    font-size: 14px;
}

.specification-help ul {
    margin: 0;
    padding-left: 20px;
}

.specification-help li {
    margin-bottom: 4px;
    font-size: 12px;
}

/* 变体规格键输入框样式 */
.field-spec_key input {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
}

/* 规格值输入框样式 */
.field-spec_value textarea {
    min-height: 60px;
    background-color: #f8f9fa;
}

/* 添加规格按钮样式 */
.add-row a {
    background-color: #28a745;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    text-decoration: none;
}

.add-row a:hover {
    background-color: #218838;
    color: white;
}
