/* 支持剪切板粘贴的图片上传Widget样式 */

.pasteable-image-widget {
    margin: 10px 0;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #f9f9f9;
    padding: 15px;
    position: relative;
    outline: none;
    max-width: 100%;
}

.pasteable-image-widget:focus {
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
}

/* 确保原始文件输入被隐藏 */
.pasteable-image-widget input[type="file"] {
    display: none !important;
}

.image-upload-area {
    position: relative;
    min-height: 150px;
    border: 2px dashed #ccc;
    border-radius: 6px;
    background: #fff;
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-upload-area:hover {
    border-color: #007cba;
    background: #f0f8ff;
}

.image-upload-area.drag-over {
    border-color: #007cba;
    background: #e6f3ff;
    transform: scale(1.02);
}

.image-upload-area.has-image {
    border-style: solid;
    border-color: #28a745;
    background: #f8fff9;
}

.pasteable-image-widget .image-upload-area.has-image .upload-instructions {
    display: none !important;
}

.pasteable-image-widget .upload-instructions {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    color: #666 !important;
    width: 100% !important;
    padding: 20px !important;
}

.pasteable-image-widget .upload-icon {
    font-size: 48px !important;
    margin-bottom: 10px !important;
    opacity: 0.7 !important;
    display: block !important;
}

.pasteable-image-widget .upload-text {
    line-height: 1.4 !important;
}

.pasteable-image-widget .upload-text strong {
    color: #333 !important;
    font-size: 16px !important;
    display: block !important;
}

.pasteable-image-widget .upload-text span {
    display: block !important;
    margin: 4px 0 !important;
    font-size: 14px !important;
}

.pasteable-image-widget .browse-btn {
    background: #007cba !important;
    color: white !important;
    border: none !important;
    padding: 4px 12px !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    text-decoration: underline !important;
    display: inline-block !important;
}

.pasteable-image-widget .browse-btn:hover {
    background: #005a87 !important;
}

.pasteable-image-widget .image-preview {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: none !important;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.95);
    overflow: hidden;
    box-sizing: border-box;
}

.pasteable-image-widget .image-preview.show {
    display: flex !important;
}

.pasteable-image-widget .image-preview img,
.pasteable-image-widget .preview-img {
    max-width: calc(100% - 20px) !important;
    max-height: 100px !important;
    width: auto !important;
    height: auto !important;
    border-radius: 4px !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
    display: block !important;
    margin: 0 auto 10px auto !important;
    object-fit: contain !important;
}

.pasteable-image-widget .image-actions {
    margin-top: 10px !important;
    text-align: center !important;
}

.pasteable-image-widget .image-actions button {
    margin: 0 5px !important;
    padding: 6px 12px !important;
    border: none !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-size: 12px !important;
    display: inline-block !important;
}

.pasteable-image-widget .remove-image-btn {
    background: #dc3545 !important;
    color: white !important;
}

.pasteable-image-widget .remove-image-btn:hover {
    background: #c82333 !important;
}

.pasteable-image-widget .change-image-btn {
    background: #6c757d !important;
    color: white !important;
}

.pasteable-image-widget .change-image-btn:hover {
    background: #5a6268 !important;
}

.upload-status {
    margin-top: 10px;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    text-align: center;
    min-height: 20px;
}

.upload-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.upload-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.upload-status.loading {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.upload-status.loading::after {
    content: "...";
    animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
    0%, 20% {
        color: rgba(0,0,0,0);
        text-shadow:
            .25em 0 0 rgba(0,0,0,0),
            .5em 0 0 rgba(0,0,0,0);
    }
    40% {
        color: #0c5460;
        text-shadow:
            .25em 0 0 rgba(0,0,0,0),
            .5em 0 0 rgba(0,0,0,0);
    }
    60% {
        text-shadow:
            .25em 0 0 #0c5460,
            .5em 0 0 rgba(0,0,0,0);
    }
    80%, 100% {
        text-shadow:
            .25em 0 0 #0c5460,
            .5em 0 0 #0c5460;
    }
}



/* 响应式设计 */
@media (max-width: 768px) {
    .pasteable-image-widget {
        padding: 10px;
    }
    
    .image-upload-area {
        min-height: 120px;
    }
    
    .upload-instructions {
        height: 120px;
    }
    
    .upload-icon {
        font-size: 36px;
    }
    
    .upload-text {
        font-size: 12px;
    }
    
    .upload-text strong {
        font-size: 14px;
    }
}
