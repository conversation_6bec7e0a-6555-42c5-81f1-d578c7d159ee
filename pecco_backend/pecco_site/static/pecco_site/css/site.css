/* CSS Variables for consistent styling */
:root {
  --font-family-primary: 'Jo<PERSON>', -apple-system, BlinkMacSystemFont, 'Inter', 'Noto Sans SC', sans-serif;
  --color-text: #232323;
  --color-text-light: #3c3c3c;
  --color-background: #ffffff;
  --color-background-light: #f8f8f8;
  --color-accent: #051c42;
  --color-accent-hover: #234bbb;
  --spacing-section: 80px;
  --spacing-large: 60px;
  --spacing-medium: 40px;
  --spacing-small: 24px;
  --container-max-width: 1400px;
  --container-padding: 40px;
}

body {
  margin: 0;
  font-family: var(--font-family-primary);
  background: var(--color-background);
  color: var(--color-text);
  font-weight: 400;
  line-height: 1.6;
}

.topbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px var(--container-padding);
  background: rgba(255,255,255,.95);
  backdrop-filter: saturate(180%) blur(10px);
  position: sticky;
  top: 0;
  border-bottom: 1px solid #e6e6e6;
  z-index: 100;
  position: relative; /* Add relative positioning for mobile tagline */
}

.topbar .left {
  display: flex;
  align-items: center;
  gap: 36px;
  flex: 1;
}

.tagline-script {
  display: flex;
  flex-direction: column;
  line-height: 1.1;
  margin-top: -2px;
  margin-left: 8px;
  cursor: default;
}

.script-line1,
.script-line2 {
  font-family: 'Dancing Script', 'Brush Script MT', cursive;
  color: var(--color-accent);
  font-weight: 600;
  font-size: 22px;
  letter-spacing: 0.4px;
  text-shadow: 0 1px 3px rgba(5,28,66,0.15);
  transition: all 0.3s ease;
}

.script-line1 {
  margin-bottom: -4px;
  transform: rotate(-1.5deg);
}

.script-line2 {
  margin-left: 12px;
  transform: rotate(1.2deg);
}

.tagline-script:hover .script-line1,
.tagline-script:hover .script-line2 {
  transform: rotate(0deg);
  color: var(--color-accent-hover);
  text-shadow: 0 2px 6px rgba(35,75,187,0.2);
}

.logo {
  width: 120px;
  height: 50px;
  transition: all .3s ease;
  filter: drop-shadow(0 2px 8px rgba(139,146,153,.15));
}

.logo:hover {
  transform: translateY(-1px);
  filter: drop-shadow(0 4px 12px rgba(139,146,153,.25));
}

/* Original tagline-bar styles - no longer used as tagline is now inline in topbar
.tagline-bar {
  background: var(--color-background-light);
  border-bottom: 1px solid #e6e6e6;
}

.tagline {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 16px var(--container-padding);
  color: var(--color-text-light);
  font-weight: 500;
  text-align: center;
  font-size: 16px;
}
*/

main.page {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.breadcrumb {
  max-width: var(--container-max-width);
  margin: 12px auto 0;
  padding: 0 var(--container-padding);
  color: #999999;
  font-size: 14px;
}

.breadcrumb a {
  color: var(--color-accent);
  text-decoration: none;
}

.breadcrumb .sep {
  margin: 0 8px;
  color: #d3d3d3;
}

.main-nav {
  display: flex;
  align-items: center;
  gap: 8px;
}

.main-nav a {
  margin: 0;
  color: var(--color-text);
  text-decoration: none;
  padding: 12px 20px;
  border-radius: 30px;
  transition: all .3s ease;
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 0.02em;
}

.main-nav a:hover {
  background: var(--color-background-light);
  color: var(--color-accent-hover);
  transform: translateY(-1px);
  box-shadow: 0 8px 24px rgba(15,23,42,.08);
}

.main-nav a.active {
  background: var(--color-accent);
  color: #fff;
  transform: translateY(-1px);
  box-shadow: 0 8px 24px rgba(5,28,66,.15);
}

.main-nav a.active:hover {
  background: var(--color-accent-hover);
  color: #fff;
}

.right {
  display: flex;
  align-items: center;
  gap: 24px;
  flex: 1;
  justify-content: flex-end;
}

/* Search Styles */
.search-container {
  position: relative;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 240px;
  padding: 10px 16px 10px 44px;
  border: 1px solid #e6e6e6;
  border-radius: 24px;
  font-size: 14px;
  font-family: var(--font-family-primary);
  background: #fafafa;
  transition: all 0.3s ease;
  outline: none;
}

.search-input:focus {
  border-color: var(--color-accent);
  background: #fff;
  box-shadow: 0 4px 12px rgba(5,28,66,0.1);
}

.search-input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.search-icon {
  position: absolute;
  left: 14px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  pointer-events: none;
  z-index: 2;
}

.search-input:focus ~ .search-icon {
  color: var(--color-accent);
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border: 1px solid #e6e6e6;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.12);
  max-height: 400px;
  overflow-y: auto;
  z-index: 1000;
  display: none;
  margin-top: 4px;
}

.search-results.show {
  display: block;
}

.search-result-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f1f5f9;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-decoration: none;
  color: inherit;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover,
.search-result-item.highlighted {
  background: #f8fafc;
}

.search-result-image {
  width: 48px;
  height: 48px;
  object-fit: cover;
  border-radius: 8px;
  margin-right: 12px;
  background: #f1f5f9;
}

.search-result-content {
  flex: 1;
}

.search-result-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text);
  margin: 0 0 4px 0;
}

.search-result-desc {
  font-size: 12px;
  color: #64748b;
  margin: 0;
  line-height: 1.4;
}

.search-result-tags {
  display: flex;
  gap: 4px;
  margin-top: 4px;
}

.search-result-tag {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 600;
  text-transform: uppercase;
}

.search-result-tag.tag-new {
  background: #dcfce7;
  color: #166534;
}

.search-result-tag.tag-hot {
  background: #fef3c7;
  color: #92400e;
}

.search-result-tag.tag-featured {
  background: #dbeafe;
  color: #1e40af;
}

.search-no-results {
  padding: 20px;
  text-align: center;
  color: #64748b;
  font-size: 14px;
}

/* Language Selector Styles */
.language-selector {
  position: relative;
  display: inline-block;
}

.language-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: transparent;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text);
  transition: all 0.3s ease;
  min-width: 80px;
  justify-content: space-between;
}

.language-toggle:hover {
  border-color: var(--color-accent);
  background: rgba(5, 28, 66, 0.02);
}

.language-toggle:focus {
  outline: none;
  border-color: var(--color-accent);
  box-shadow: 0 0 0 3px rgba(5, 28, 66, 0.1);
}

.current-lang {
  font-weight: 600;
}

.dropdown-icon {
  transition: transform 0.3s ease;
  color: var(--color-text-light);
}

.language-selector:hover .dropdown-icon {
  transform: rotate(180deg);
}

.language-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 4px;
  background: white;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px);
  transition: all 0.3s ease;
  z-index: 1000;
  min-width: 160px;
}

.language-selector:hover .language-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.lang-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  text-decoration: none;
  color: var(--color-text);
  transition: all 0.2s ease;
  border-bottom: 1px solid #f5f5f5;
}

.lang-option:last-child {
  border-bottom: none;
}

.lang-option:hover {
  background: var(--color-background-light);
  color: var(--color-accent);
}

.lang-option.active {
  background: #3b82f6;
  color: white;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.lang-code {
  font-weight: 600;
  font-size: 14px;
  min-width: 30px;
}

.lang-name {
  font-size: 14px;
  font-weight: 500;
}

.lang-option.active .lang-code,
.lang-option.active .lang-name {
  color: white;
  font-weight: 700;
}

/* Mobile Menu Toggle Button */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 101;
}

.mobile-menu-toggle span {
  width: 24px;
  height: 2px;
  background: var(--color-text);
  border-radius: 1px;
  transition: all 0.3s ease;
  transform-origin: center;
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Navigation */
.mobile-nav {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: saturate(180%) blur(10px);
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-nav-header {
  position: absolute;
  top: 0;
  right: 0;
  padding: 20px;
  z-index: 102;
}

.mobile-nav-close {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  color: var(--color-text);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-nav-close:hover {
  background: rgba(0, 0, 0, 0.05);
  color: var(--color-accent);
}

.mobile-nav.active {
  opacity: 1;
  visibility: visible;
}

/* Mobile topbar tagline styles - shown in center of topbar on mobile */
.mobile-topbar-tagline {
  display: none; /* Hidden by default, shown only on mobile */
  flex-direction: column;
  align-items: center;
  justify-content: center;
  line-height: 1.1;
  cursor: default;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.mobile-topbar-script-line1,
.mobile-topbar-script-line2 {
  font-family: 'Dancing Script', 'Brush Script MT', cursive;
  color: var(--color-accent);
  font-weight: 600;
  font-size: 18px;
  letter-spacing: 0.4px;
  text-shadow: 0 1px 3px rgba(5,28,66,0.15);
  transition: all 0.3s ease;
  text-align: center;
}

.mobile-topbar-script-line1 {
  margin-bottom: -2px;
  transform: rotate(-1.5deg);
}

.mobile-topbar-script-line2 {
  margin-left: 8px;
  transform: rotate(1.2deg);
}

.mobile-topbar-tagline:hover .mobile-topbar-script-line1,
.mobile-topbar-tagline:hover .mobile-topbar-script-line2 {
  transform: rotate(0deg);
  color: var(--color-accent-hover);
  text-shadow: 0 2px 6px rgba(35,75,187,0.2);
}

.mobile-nav-content {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  height: 100%;
  padding: 100px 20px 40px;
  overflow-y: auto;
  max-height: 100vh;
  box-sizing: border-box;
}

.mobile-nav-content a {
  color: var(--color-text);
  text-decoration: none;
  font-size: 20px;
  font-weight: 500;
  padding: 12px 0;
  margin: 6px 0;
  border-bottom: 1px solid transparent;
  transition: all 0.3s ease;
  text-align: center;
  width: 100%;
  max-width: 300px;
}

.mobile-nav-content a:hover {
  color: var(--color-accent-hover);
  border-bottom-color: var(--color-accent-hover);
}

.mobile-nav-content a.active {
  color: var(--color-accent);
  border-bottom-color: var(--color-accent);
}

.mobile-lang-switcher {
  margin-top: 30px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.mobile-language-selector {
  width: 100%;
  max-width: 300px;
}

.mobile-lang-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mobile-lang-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  text-decoration: none;
  color: var(--color-text);
  transition: all 0.2s ease;
  border: 1px solid transparent;
  border-radius: 8px;
  background: transparent;
}

.mobile-lang-option:hover {
  background: var(--color-background-light);
  border-color: var(--color-accent-hover);
  color: var(--color-accent-hover);
}

.mobile-lang-option.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
  font-weight: 700;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.mobile-lang-code {
  font-weight: 600;
  font-size: 14px;
  min-width: 35px;
}

.mobile-lang-name {
  font-size: 14px;
  font-weight: 500;
}

.mobile-lang-option.active .mobile-lang-code,
.mobile-lang-option.active .mobile-lang-name {
  color: white;
  font-weight: 700;
}

.mobile-lang-switcher .divider {
  color: #d3d3d3;
  font-weight: 300;
}

.right .divider {
  color: #d3d3d3;
  margin: 0 4px;
}
/* Hero Section */
.hero {
  margin: 0;
  position: relative;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
}

.hero .carousel {
  position: relative;
  height: 800px;
  overflow: hidden;
}

.hero .slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 0;
  overflow: hidden;
  margin: 0;
  background: linear-gradient(180deg, rgba(0,0,0,.3), rgba(0,0,0,.1));
  box-shadow: none;
  opacity: 0;
  transition: opacity 0.8s ease-in-out;
}

.hero .slide.active {
  opacity: 1;
}

.hero .slide::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(5,28,66,.2) 0%, rgba(0,0,0,.4) 100%);
  z-index: 1;
}

.hero .slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.hero .slide .overlay {
  position: absolute;
  left: var(--container-padding);
  bottom: 80px;
  right: var(--container-padding);
  color: #fff;
  text-shadow: 0 4px 12px rgba(0,0,0,.6);
  max-width: 600px;
  z-index: 2;
}

.hero .slide .overlay h2 {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 16px;
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.hero .slide .overlay p {
  font-size: 20px;
  font-weight: 400;
  margin: 0 0 32px;
  opacity: 0.95;
  line-height: 1.5;
}

.hero .slide .overlay .btn {
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  border-radius: 24px;
}

/* Carousel Indicators */
.hero .carousel-indicators {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 12px;
  z-index: 10;
}

.hero .carousel-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
}

.hero .carousel-indicator.active {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 255, 255, 1);
  transform: scale(1.2);
}

.hero .carousel-indicator:hover {
  background: rgba(255, 255, 255, 0.7);
  border-color: rgba(255, 255, 255, 0.9);
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 16px 32px;
  border-radius: 30px;
  background: var(--color-accent);
  color: #fff;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  letter-spacing: 0.02em;
  transition: all .3s ease;
  border: 2px solid var(--color-accent);
}

.btn:hover {
  background: var(--color-accent-hover);
  border-color: var(--color-accent-hover);
  transform: translateY(-2px);
  box-shadow: 0 12px 30px rgba(5,28,66,.20);
}
/* Section Headers */
.section-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 0 var(--spacing-medium);
  padding: 0;
  flex-direction: column;
  gap: var(--spacing-small);
}

.section-title {
  font-size: 30px;
  font-weight: 700;
  color: var(--color-text);
  line-height: 1.3;
  letter-spacing: -0.02em;
  margin: 0;
  text-align: center;
}

.section-action a {
  color: var(--color-accent);
  text-decoration: none;
  font-weight: 700;
  font-size: 30px;
  padding: 8px 16px;
  border-radius: 20px;
  border-bottom: 2px solid var(--color-accent);
  transition: all .3s ease;
}

.section-action a:hover {
  background: var(--color-background-light);
  color: var(--color-accent-hover);
  border-bottom: 2px solid var(--color-accent-hover);
}

/* 为"Learn More"和"View All"按钮添加较小样式 */
.section-action a.small-btn {
  font-size: 14px;
  padding: 4px 10px;
  border-radius: 12px;
  font-weight: 600;
}

.section-subtitle {
  font-size: 18px;
  color: var(--color-text-light);
  margin: 0;
  max-width: 600px;
  text-align: center;
  line-height: 1.6;
}
/* About Us Section */
.about-us-section {
  padding: var(--spacing-section) 0;
  background: var(--color-background);
}

.about-us-section .about-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.about-us-section .about-content p {
  font-size: 18px;
  line-height: 1.6;
  color: var(--color-text-light);
  margin: 0;
}

/* Categories Section */
.categories {
  padding: var(--spacing-section) 0;
  background: var(--color-background);
}

.categories .grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 250px));
  gap: var(--spacing-medium);
  justify-content: center;
  justify-items: center;
}

.category-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-medium) var(--spacing-small);
  border-radius: 20px;
  background: var(--color-background);
  text-decoration: none;
  color: var(--color-text);
  box-shadow: 0 12px 30px rgba(15,23,42,.08);
  transition: all .3s ease;
  border: 1px solid #f0f0f0;
  min-height: 280px;
  justify-content: center;
}

.category-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 50px rgba(15,23,42,.12);
  border-color: #e0e0e0;
}

.category-card .avatar {
  width: 140px;
  height: 140px;
  border-radius: 50%;
  box-shadow: 0 8px 20px rgba(15,23,42,.08);
  background: var(--color-background);
  object-fit: cover;
  border: 3px solid #f8f8f8;
}

.category-card .name {
  margin-top: 20px;
  font-weight: 600;
  font-size: 18px;
  text-align: center;
  color: var(--color-text);
}
/* Product List Section */
.product-list {
  padding: var(--spacing-section) 0;
  background: var(--color-background-light);
}

.product-list h3 {
  font-size: 30px;
  font-weight: 700;
  color: var(--color-text);
  margin: 0 0 var(--spacing-medium);
  text-align: center;
}

.product-list .grid,
.testimonials .grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-medium);
}

/* Product Card Styles */
.product-card {
  display: block;
  padding: var(--spacing-small);
  border-radius: 20px;
  background: var(--color-background);
  box-shadow: 0 12px 30px rgba(15,23,42,.08);
  color: var(--color-text);
  text-decoration: none;
  transition: all .3s ease;
  border: 1px solid #f0f0f0;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 50px rgba(15,23,42,.12);
  border-color: #e0e0e0;
}

.product-card .image-container {
  position: relative;
  width: 100%;
  height: 220px;
  border-radius: 16px;
  overflow: hidden;
}

.product-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.4s ease, transform .3s ease;
}

.product-card .hover-image {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.product-card:hover .cover-image {
  transform: scale(1.03);
}

.product-card:hover .hover-image {
  opacity: 1;
  transform: scale(1.03);
}

.product-card .product-info {
  padding: 16px 0;
}

.product-card h4,
.product-card .product-info h4 {
  margin: 16px 0 8px;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text);
  line-height: 1.4;
}

.product-card p,
.product-card .product-info p {
  margin: 0 0 12px;
  color: var(--color-text-light);
  font-size: 14px;
  line-height: 1.5;
}

.product-card .tags {
  margin-top: 12px;
}

.product-card .tag {
  display: inline-block;
  margin-right: 8px;
  margin-top: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  background: var(--color-background-light);
  color: var(--color-text-light);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.product-card .tag-featured {
  background: var(--color-accent);
  color: #fff;
}

.product-card .tag-hot {
  background: #ff4757;
  color: #fff;
}

.product-card .tag-new {
  background: #2ed573;
  color: #fff;
}
/* Stories Section */
.stories {
  padding: var(--spacing-section) 0 !important;
  background: var(--color-background-light);
}

.stories h3 {
  font-size: 30px;
  font-weight: 700;
  color: var(--color-text);
  margin: 0 0 var(--spacing-medium);
  text-align: center;
}

.story-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-medium);
}

/* Testimonials Section */
.testimonials {
  padding: var(--spacing-section) 0 !important;
  background: var(--color-background);
}

.testimonials h3 {
  font-size: 30px;
  font-weight: 700;
  color: var(--color-text);
  margin: 0 0 var(--spacing-medium);
  text-align: center;
}

.testimonials .product-card {
  padding: var(--spacing-medium);
}

.testimonials .product-card p {
  font-style: italic;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 16px;
  color: var(--color-text-light);
}

/* Product Detail */
.product-detail {
  display: grid;
  grid-template-columns: 1.2fr .8fr;
  gap: var(--spacing-large);
  padding: var(--spacing-section) 0;
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding-left: var(--container-padding);
  padding-right: var(--container-padding);
}

/* Button reset for product options */
.product-options button {
  border: none;
  background: none;
  padding: 0;
  margin: 0;
  font: inherit;
  cursor: pointer;
  outline: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Gallery Styles */
.product-detail .gallery {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.main-image-container {
  position: relative;
  width: 100%;
  max-width: 500px; /* 限制最大宽度 */
  height: 500px; /* 固定高度 */
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 12px 30px rgba(15,23,42,.08);
  background: #f8f9fa;
}

.main-image {
  width: 100%;
  height: 100%; /* 填满容器高度 */
  object-fit: cover; /* 保持比例，裁剪以填满容器 */
  display: block;
  transition: transform 0.3s ease, opacity 0.4s ease;
}

.cover-image {
  position: relative;
  z-index: 1;
}

.hover-image {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  z-index: 2;
  transition: opacity 0.4s ease;
}

.main-image-container:hover .cover-image {
  transform: scale(1.02);
}

.main-image-container:hover .hover-image {
  opacity: 1;
  transform: scale(1.02);
}

/* 选择变体后禁用hover效果 */
.main-image-container.variant-selected:hover .hover-image {
  opacity: 0 !important;
  transform: none !important;
}

.thumbnail-gallery {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 20px;
  max-width: 100%;
  overflow-x: auto;
  padding: 5px 0;
}

.thumbnail {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 12px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  opacity: 0.7;
  flex-shrink: 0;
}

.thumbnail:hover {
  opacity: 1;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(15,23,42,.15);
}

.thumbnail.active {
  border-color: var(--color-accent);
  opacity: 1;
  box-shadow: 0 8px 20px rgba(15,23,42,.15);
}

/* Product Info Styles */
.product-info {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.product-header {
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 20px;
}

.product-title {
  font-size: 28px;
  font-weight: 600;
  color: #0F2A43;
  margin: 0 0 12px 0;
  line-height: 1.3;
}

.product-description {
  font-size: 16px;
  color: #475569;
  margin: 0 0 16px 0;
  line-height: 1.6;
}

.product-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.product-tags .tag {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.product-tags .tag-featured {
  background: var(--color-accent);
  color: #fff;
}

.product-tags .tag-hot {
  background: #ff4757;
  color: #fff;
}

.product-tags .tag-new {
  background: #2ed573;
  color: #fff;
}

/* Product Options */
.product-options {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-title {
  font-size: 16px;
  font-weight: 600;
  color: #334155;
  margin: 0;
}

/* Size Options */
.size-options {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.product-options .size-option {
  padding: 16px 24px !important;
  border: 2px solid #e2e8f0 !important;
  border-radius: 12px !important;
  background: white !important;
  color: #374151 !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  font-size: 16px !important;
  min-width: 120px !important;
  text-align: center !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05) !important;
  position: relative !important;
  outline: none !important;
}

.product-options .size-option:hover {
  border-color: #9ca3af !important;
  background: #f9fafb !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
}

.product-options .size-option.selected {
  border-color: #1f2937 !important;
  background: #1f2937 !important;
  color: white !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(31,41,55,0.3) !important;
}

/* Color Options */
.color-options {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  align-items: center;
}

.product-options .color-option {
  width: 50px !important;
  height: 50px !important;
  border-radius: 50% !important;
  border: 3px solid #e5e7eb !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 11px !important;
  font-weight: 600 !important;
  color: #6b7280 !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
  outline: none !important;
}

.product-options .color-option:hover {
  transform: scale(1.05) !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
  border-color: #9ca3af !important;
}

.product-options .color-option.selected {
  border-color: #1f2937 !important;
  box-shadow: 0 0 0 2px white, 0 0 0 4px #1f2937 !important;
  transform: scale(1.05) !important;
}

/* Product Details Tabs */
.product-details {
  border-top: 1px solid #e2e8f0;
  padding-top: 30px;
}

.detail-tabs {
  display: flex;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

.tab-button {
  padding: 12px 24px;
  border: none;
  background: none;
  color: #64748b;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
  font-size: 14px;
}

.tab-button:hover {
  color: #334155;
}

.tab-button.active {
  color: var(--color-accent);
  border-bottom-color: var(--color-accent);
}

.tab-content {
  min-height: 200px;
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

.rich-content {
  color: #475569;
  line-height: 1.7;
}

.rich-content p {
  margin-bottom: 16px;
}

.rich-content h3, .rich-content h4 {
  color: #334155;
  margin: 24px 0 12px 0;
}

/* Specifications */
.specifications-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.spec-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: #334155;
  margin: 0 0 16px 0;
}

.spec-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.spec-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f1f5f9;
}

.spec-item:last-child {
  border-bottom: none;
}

.spec-key {
  font-weight: 500;
  color: #64748b;
  flex: 0 0 40%;
}

.spec-value {
  color: #334155;
  text-align: right;
  flex: 1;
}

/* Flash Messages */
.flash-area {
  position: fixed;
  top: 90px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  z-index: 999;
}

.flash {
  background: #e6ffef;
  color: #147d3f;
  border: 1px solid #b7f0cc;
  padding: 16px 24px;
  border-radius: 20px;
  box-shadow: 0 12px 30px rgba(15,23,42,.08);
  font-weight: 500;
}

/* Footer */
.footer {
  padding: var(--spacing-large) var(--container-padding);
  color: var(--color-text-light);
  text-align: center;
  background: var(--color-background-light);
  border-top: 1px solid #e6e6e6;
  font-size: 14px;
}

/* Responsive Design */

/* Tablet and small desktop */
@media (max-width: 1024px) {
  :root {
    --container-padding: 30px;
    --spacing-section: 70px;
    --spacing-large: 50px;
    --spacing-medium: 35px;
  }

  .hero .carousel {
    height: 600px;
  }

  .hero .slide .overlay h2 {
    font-size: 40px;
  }

  .hero .slide .overlay p {
    font-size: 18px;
  }

  .categories .grid,
  .product-list .grid,
  .testimonials .grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
  }

  /* Adjust tagline font size for tablets */
  .script-line1,
  .script-line2 {
    font-size: 16px;
  }

  .topbar .left {
    gap: 20px;
  }
}

/* Mobile landscape and small tablets */
@media (max-width: 768px) {
  :root {
    --container-padding: 20px;
    --spacing-section: 60px;
    --spacing-large: 40px;
    --spacing-medium: 30px;
  }

  /* Show mobile menu toggle, hide desktop nav */
  .mobile-menu-toggle {
    display: flex;
  }

  .right .main-nav {
    display: none;
  }

  .right .search-container {
    display: none;
  }

  .right .language-selector {
    display: none;
  }

  .mobile-nav {
    display: block;
  }

  /* Hide desktop tagline on mobile to save space */
  .tagline-script {
    display: none;
  }

  /* Show mobile tagline in center of topbar */
  .mobile-topbar-tagline {
    display: flex;
  }

  /* Adjust topbar for mobile */
  .topbar {
    padding: 15px var(--container-padding);
  }

  .logo {
    width: 96px;
    height: 40px;
  }

  .product-detail {
    grid-template-columns: 1fr;
    gap: var(--spacing-medium);
  }

  .thumbnail-gallery {
    justify-content: center;
  }

  .thumbnail {
    width: 70px;
    height: 70px;
  }

  .product-title {
    font-size: 24px;
  }

  .size-options {
    justify-content: center;
  }

  .color-options {
    justify-content: center;
  }

  .hero .carousel {
    height: 500px;
  }

  .hero .slide .overlay {
    left: var(--container-padding);
    bottom: 60px;
    right: var(--container-padding);
  }

  .hero .slide .overlay h2 {
    font-size: 32px;
    line-height: 1.2;
  }

  .hero .slide .overlay p {
    font-size: 16px;
    margin-bottom: 24px;
  }

  .section-title {
    font-size: 28px;
  }

  .categories .grid,
  .product-list .grid,
  .testimonials .grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
  }

  /* Product Detail Mobile */
  .main-image-container {
    max-width: 100%;
    height: 350px; /* 移动端较小的固定高度 */
  }

  .product-info {
    gap: 20px;
  }

  .product-options {
    gap: 16px;
  }

  .option-title {
    font-size: 14px;
  }

  .product-options .size-option {
    padding: 10px 16px !important;
    font-size: 13px !important;
    min-width: 70px !important;
  }

  .product-options .color-option {
    width: 35px !important;
    height: 35px !important;
  }

  .detail-tabs {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .tab-button {
    padding: 10px 16px;
    font-size: 13px;
    white-space: nowrap;
  }

  .spec-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .spec-key {
    flex: none;
  }

  .spec-value {
    text-align: left;
  }
}

/* Mobile portrait */
@media (max-width: 480px) {
  :root {
    --container-padding: 16px;
    --spacing-section: 50px;
    --spacing-large: 32px;
    --spacing-medium: 24px;
  }

  .topbar {
    padding: 12px var(--container-padding);
  }

  .logo {
    width: 84px;
    height: 36px;
  }

  .hero .carousel {
    height: 400px;
  }

  .hero .slide .overlay {
    left: var(--container-padding);
    bottom: 40px;
    right: var(--container-padding);
  }

  .hero .slide .overlay h2 {
    font-size: 28px;
    line-height: 1.2;
    margin-bottom: 12px;
  }

  .hero .slide .overlay p {
    font-size: 16px;
    margin-bottom: 20px;
  }

  .section-title {
    font-size: 24px;
  }

  .categories .grid,
  .product-list .grid,
  .testimonials .grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .mobile-nav-content a {
    font-size: 16px;
    padding: 8px 0;
  }

  .mobile-nav-content {
    padding: 80px 16px 30px;
  }

  /* Adjust mobile topbar tagline for small screens */
  .mobile-topbar-script-line1,
  .mobile-topbar-script-line2 {
    font-size: 16px;
  }

  .mobile-lang-option {
    padding: 10px 12px;
  }

  .mobile-lang-code,
  .mobile-lang-name {
    font-size: 13px;
  }

  .mobile-lang-switcher {
    margin-top: 20px;
  }

  .tagline {
    font-size: 14px;
    padding: 12px var(--container-padding);
  }

  /* Product Detail Mobile Portrait */
  .thumbnail {
    width: 60px !important;
    height: 60px !important;
  }

  .product-title {
    font-size: 20px !important;
  }

  .product-options .size-option {
    padding: 8px 12px !important;
    font-size: 12px !important;
    min-width: 60px !important;
  }

  .product-options .color-option {
    width: 30px !important;
    height: 30px !important;
    font-size: 10px !important;
  }
}

/* Contact Page Styles */
.contact {
  padding: var(--spacing-section) 0;
}

.contact .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.contact .section-header {
  text-align: center;
  margin-bottom: var(--spacing-large);
}

.contact .section-title {
  font-size: 36px;
  font-weight: 700;
  color: var(--color-text);
  margin: 0 0 16px;
  line-height: 1.2;
}

.contact .section-subtitle {
  font-size: 18px;
  color: var(--color-text-light);
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--spacing-large);
  align-items: start;
}

.contact-info {
  background: var(--color-background-light);
  border-radius: 20px;
  padding: var(--spacing-medium);
  box-shadow: 0 10px 24px rgba(15,23,42,.06);
}

.contact-info h3 {
  font-size: 24px;
  font-weight: 600;
  color: var(--color-text);
  margin: 0 0 24px;
}

.info-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-row {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.08em;
  margin-bottom: 2px;
}

.info-value {
  font-size: 16px;
  color: var(--color-text);
  font-weight: 500;
  line-height: 1.4;
}

.contact-form-wrapper {
  background: #fff;
  border-radius: 20px;
  padding: var(--spacing-medium);
  box-shadow: 0 10px 24px rgba(15,23,42,.06);
  border: 1px solid #e6e6e6;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text);
  margin: 0;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e6e6e6;
  border-radius: 12px;
  font-size: 16px;
  font-family: var(--font-family-primary);
  color: var(--color-text);
  background: #fff;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-accent);
  box-shadow: 0 0 0 3px rgba(5, 28, 66, 0.1);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: #999;
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
  line-height: 1.6;
}

.form-error {
  color: #dc2626;
  font-size: 14px;
  margin-top: 4px;
}

.btn-primary {
  background: var(--color-accent);
  color: #fff;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-start;
  min-width: 160px;
}

.btn-primary:hover {
  background: var(--color-accent-hover);
  transform: translateY(-1px);
  box-shadow: 0 8px 24px rgba(5, 28, 66, 0.2);
}

.btn-primary:active {
  transform: translateY(0);
}

/* Contact Page Responsive */
@media (max-width: 768px) {
  .contact .section-title {
    font-size: 28px;
  }

  .contact .section-subtitle {
    font-size: 16px;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-medium);
  }

  .contact-info,
  .contact-form-wrapper {
    padding: 24px;
  }
}

@media (max-width: 480px) {
  .contact .section-title {
    font-size: 24px;
  }

  .contact-info,
  .contact-form-wrapper {
    padding: 20px;
  }

  .form-input,
  .form-textarea {
    padding: 14px 16px;
  }

  .btn-primary {
    width: 100%;
    padding: 16px;
  }
}

/* Reseller Section */
.reseller-section {
  padding: var(--spacing-section) 0;
  background: var(--color-background);
  margin-top: var(--spacing-large);
}

.reseller-content {
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 500px;
  margin: 0 auto;
}

.reseller-form {
  width: 100%;
}

.reseller-form .form-group {
  display: flex;
  flex-direction: row;
  gap: 12px;
  align-items: center;
}

.reseller-form .form-input {
  flex: 1;
  max-width: 350px;
  margin: 0;
  display: block;
}

.reseller-form .btn {
  white-space: nowrap;
  margin: 0;
}

/* Reseller Section Responsive */
@media (max-width: 768px) {
  .reseller-section {
    margin-top: var(--spacing-medium);
  }
  
  .reseller-form .form-group {
    flex-direction: column;
    gap: 16px;
  }
  
  .reseller-form .form-input {
    max-width: 100%;
  }
  
  .reseller-form .btn {
    width: 100%;
  }
}



/* Footer */
.footer {
  background: var(--color-background-light);
  border-top: 1px solid #e6e6e6;
  padding: var(--spacing-large) 0 var(--spacing-medium);
}

.footer-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 1.5fr 1fr;
  gap: var(--spacing-large);
  margin-bottom: var(--spacing-medium);
  align-items: start;
}

.footer-column h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text);
  margin: 0 0 20px;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-links a {
  color: var(--color-text-light);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--color-accent);
}



/* Footer Company Column */
.footer-company {
  text-align: left;
}

/* Footer Newsletter Column */
.footer-newsletter {
  text-align: center;
}

.newsletter-section {
  max-width: 400px;
  margin: 0 auto;
}

.newsletter-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--color-text);
  margin: 0 0 12px;
  line-height: 1.2;
}

.newsletter-subtitle {
  font-size: 14px;
  color: var(--color-text-light);
  margin: 0 0 24px;
  line-height: 1.4;
}

.newsletter-form {
  margin-bottom: 16px;
}

.newsletter-input-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.newsletter-input {
  flex: 1;
  padding: 14px 20px;
  border: 2px solid #e5e7eb;
  border-radius: 50px;
  font-size: 14px;
  color: var(--color-text);
  background: #fff;
  transition: all 0.3s ease;
}

.newsletter-input:focus {
  outline: none;
  border-color: var(--color-accent);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.newsletter-input::placeholder {
  color: #9ca3af;
}

.newsletter-btn {
  padding: 14px 32px;
  background: #3b82f6;
  color: #fff;
  border: none;
  border-radius: 50px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.newsletter-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.newsletter-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.newsletter-message {
  min-height: 20px;
  font-size: 14px;
}

.success-message {
  color: #059669;
  font-weight: 500;
}

.error-message {
  color: #dc2626;
  font-weight: 500;
}



.company-name {
  font-size: 20px;
  font-weight: 700;
  color: var(--color-accent);
  margin-bottom: 12px;
}

.company-address {
  font-size: 14px;
  color: var(--color-text-light);
  line-height: 1.5;
}

/* Footer Navigation Column */
.footer-nav {
  text-align: right;
}

.footer-nav .footer-links {
  text-align: right;
}

.footer-nav .footer-links a {
  font-size: 16px;
  font-weight: 500;
}

.contact-info {
  margin-bottom: 24px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
  font-size: 14px;
  color: var(--color-text-light);
  line-height: 1.5;
}

.contact-icon {
  font-size: 16px;
  margin-top: 2px;
}

.social-icons {
  display: flex;
  gap: 12px;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--color-accent);
  color: #fff;
  border-radius: 50%;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.social-icon:hover {
  background: var(--color-accent-hover);
  transform: translateY(-2px);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-medium);
  border-top: 1px solid #e6e6e6;
}

.copyright {
  font-size: 14px;
  color: var(--color-text-light);
  font-weight: 500;
}

.icp-info {
  font-size: 14px;
  color: var(--color-text-light);
  font-weight: 500;
}

.icp-info a {
  color: var(--color-text-light);
  text-decoration: none;
  transition: color 0.3s ease;
}

.icp-info a:hover {
  color: var(--color-accent);
  text-decoration: underline;
}



/* Footer Responsive */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-medium);
  }

  .footer-company {
    order: 1;
    text-align: center;
  }

  .footer-newsletter {
    order: 2;
  }

  .newsletter-input-group {
    flex-direction: column;
    gap: 16px;
  }

  .newsletter-input {
    width: 100%;
  }

  .newsletter-btn {
    width: 100%;
    padding: 16px 32px;
  }

  .footer-nav {
    order: 3;
    text-align: center;
  }

  .footer-nav .footer-links {
    text-align: center;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .icp-info {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .social-icons {
    gap: 8px;
  }

  .social-icon {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }
}

/* Product List Page */
.product-list {
  padding: var(--spacing-large) 0;
}

.product-header {
  margin-bottom: var(--spacing-large);
}

.product-header h3 {
  margin: 0 0 var(--spacing-medium);
  font-size: 32px;
  font-weight: 700;
  color: var(--color-text);
  text-align: center;
}

/* Category Filter */
.category-filter {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-medium);
}

.filter-tabs {
  display: flex;
  gap: 8px;
  background: #f8fafc;
  padding: 6px;
  border-radius: 50px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.filter-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 50px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-light);
  transition: all 0.3s ease;
  white-space: nowrap;
}

.filter-tab:hover {
  color: var(--color-accent);
  background: rgba(59, 130, 246, 0.1);
}

.filter-tab.active {
  background: var(--color-accent);
  color: #fff;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.category-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

/* Category Info */
.category-info {
  text-align: center;
  margin-bottom: var(--spacing-medium);
}

.category-info h4 {
  margin: 0 0 8px;
  font-size: 24px;
  font-weight: 600;
  color: var(--color-text);
}

.category-count {
  margin: 0;
  font-size: 14px;
  color: var(--color-text-light);
}

/* Products Grid */
.products-grid {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.no-products {
  text-align: center;
  padding: var(--spacing-large);
  color: var(--color-text-light);
}

.no-products p {
  font-size: 16px;
  margin: 0;
}

/* Removed duplicate product card styles - consolidated above */

/* Responsive Design */
@media (max-width: 768px) {
  .filter-tabs {
    flex-wrap: wrap;
    justify-content: center;
    gap: 6px;
    padding: 8px;
  }

  .filter-tab {
    padding: 10px 16px;
    font-size: 13px;
  }

  .category-icon {
    width: 16px;
    height: 16px;
  }

  .product-header h3 {
    font-size: 28px;
  }

  .category-info h4 {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .filter-tabs {
    padding: 6px;
  }

  .filter-tab {
    padding: 8px 12px;
    font-size: 12px;
  }

  .product-header h3 {
    font-size: 24px;
  }

  .product-info {
    padding: 16px;
  }

  .product-info h4 {
    font-size: 16px;
  }
}

/* About Page Styles */
.about-content {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-large) var(--container-padding);
  line-height: 1.6;
}

.about-content h2 {
  font-size: 32px;
  font-weight: 700;
  color: var(--color-text);
  margin: 0 0 var(--spacing-medium);
  text-align: center;
}

.about-content h3 {
  font-size: 24px;
  font-weight: 600;
  color: var(--color-text);
  margin: var(--spacing-large) 0 var(--spacing-medium);
  border-bottom: 2px solid var(--color-accent);
  padding-bottom: 8px;
}

.about-content p {
  font-size: 16px;
  color: var(--color-text-light);
  margin: 0 0 var(--spacing-medium);
  text-align: justify;
}

.about-content ul {
  margin: 0 0 var(--spacing-medium);
  padding-left: 0;
  list-style: none;
}

.about-content li {
  font-size: 16px;
  color: var(--color-text-light);
  margin: 0 0 var(--spacing-small);
  padding-left: 24px;
  position: relative;
}

.about-content li:before {
  content: "•";
  color: var(--color-accent);
  font-weight: bold;
  position: absolute;
  left: 0;
}

.about-content strong {
  color: var(--color-text);
  font-weight: 600;
}

.about-content a {
  color: var(--color-accent);
  text-decoration: none;
  font-weight: 500;
}

.about-content a:hover {
  text-decoration: underline;
}

.company-intro {
  text-align: center;
  margin-bottom: var(--spacing-large);
  padding: var(--spacing-large);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 16px;
}

.company-intro p {
  font-size: 18px;
  font-weight: 500;
  color: var(--color-text);
  margin: 0;
}

.our-mission,
.our-values,
.contact-info {
  margin-bottom: var(--spacing-large);
}

/* Responsive Design for About Page */
@media (max-width: 768px) {
  .about-content {
    padding: var(--spacing-medium) var(--container-padding);
  }

  .about-content h2 {
    font-size: 28px;
  }

  .about-content h3 {
    font-size: 20px;
  }

  .about-content p,
  .about-content li {
    font-size: 15px;
  }

  .company-intro {
    padding: var(--spacing-medium);
  }

  .company-intro p {
    font-size: 16px;
  }
}

/* 懒加载图片样式 */
.lazy-image-container {
  position: relative;
  overflow: hidden;
}

.lazy-image {
  transition: opacity 0.3s ease, filter 0.3s ease;
  width: 100%;
  height: auto;
}

.lazy-image.loading {
  opacity: 0.7;
  filter: blur(2px);
}

.lazy-image.loaded {
  opacity: 1;
  filter: none;
}

.lazy-image.error {
  opacity: 0.5;
  filter: grayscale(100%);
}

.image-placeholder {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
  min-height: 200px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 响应式图片 */
.responsive-image {
  max-width: 100%;
  height: auto;
}

/* 产品图片优化 */
.product-image {
  transition: transform 0.3s ease;
}

.product-image:hover {
  transform: scale(1.05);
}

/* 轮播图优化 */
.carousel-image {
  object-fit: cover;
  width: 100%;
  height: 100%;
}
