(function(){
  // Enhanced carousel with indicators
  const carousels = document.querySelectorAll('.carousel');
  carousels.forEach(function(carousel){
    const slides = carousel.querySelectorAll('.slide');
    const indicators = carousel.querySelectorAll('.carousel-indicator');
    let currentIndex = 0;
    let autoPlayInterval;
    const CAROUSEL_INTERVAL = 5000; // 5 seconds

    if (slides.length <= 1) return;

    function showSlide(index) {
      // Remove active class from all slides and indicators
      slides.forEach(slide => slide.classList.remove('active'));
      indicators.forEach(indicator => indicator.classList.remove('active'));

      // Add active class to current slide and indicator
      slides[index].classList.add('active');
      if (indicators[index]) {
        indicators[index].classList.add('active');
      }

      currentIndex = index;
    }

    function nextSlide() {
      const nextIndex = (currentIndex + 1) % slides.length;
      showSlide(nextIndex);
    }

    function startAutoPlay() {
      // Clear any existing interval first to prevent multiple intervals
      stopAutoPlay();
      autoPlayInterval = setInterval(nextSlide, CAROUSEL_INTERVAL);
    }

    function stopAutoPlay() {
      if (autoPlayInterval) {
        clearInterval(autoPlayInterval);
        autoPlayInterval = null;
      }
    }

    // Add click handlers to indicators
    indicators.forEach((indicator, index) => {
      indicator.addEventListener('click', () => {
        showSlide(index);
        // Restart auto-play with fresh timing after manual interaction
        startAutoPlay();
      });
    });

    // Pause auto-play on hover
    carousel.addEventListener('mouseenter', stopAutoPlay);
    carousel.addEventListener('mouseleave', startAutoPlay);

    // Start auto-play
    startAutoPlay();
  });

  // Mobile menu functionality
  const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
  const mobileNav = document.querySelector('.mobile-nav');
  const mobileNavClose = document.querySelector('.mobile-nav-close');
  const body = document.body;

  function closeMobileMenu() {
    mobileMenuToggle.classList.remove('active');
    mobileNav.classList.remove('active');
    body.style.overflow = '';
  }

  function openMobileMenu() {
    mobileMenuToggle.classList.add('active');
    mobileNav.classList.add('active');
    body.style.overflow = 'hidden';
  }

  if (mobileMenuToggle && mobileNav) {
    mobileMenuToggle.addEventListener('click', function() {
      const isActive = mobileMenuToggle.classList.contains('active');
      if (isActive) {
        closeMobileMenu();
      } else {
        openMobileMenu();
      }
    });

    // Close menu with close button
    if (mobileNavClose) {
      mobileNavClose.addEventListener('click', closeMobileMenu);
    }

    // Close menu when clicking on a link
    const mobileNavLinks = mobileNav.querySelectorAll('a');
    mobileNavLinks.forEach(link => {
      link.addEventListener('click', closeMobileMenu);
    });

    // Close menu when clicking outside
    mobileNav.addEventListener('click', function(e) {
      if (e.target === mobileNav) {
        closeMobileMenu();
      }
    });

    // Close menu on escape key
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape' && mobileNav.classList.contains('active')) {
        closeMobileMenu();
      }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
      if (window.innerWidth > 768 && mobileNav.classList.contains('active')) {
        closeMobileMenu();
      }
    });
  }

  // Lazy load images: add loading=lazy for non-first images
  document.querySelectorAll('img').forEach((img, i)=>{
    if (!img.getAttribute('loading')) {
      img.setAttribute('loading', i === 0 ? 'eager' : 'lazy');
    }
    img.setAttribute('decoding','async');
  });

  // Product image hover effect - preload hover images for better performance
  function preloadHoverImages() {
    const hoverImages = document.querySelectorAll('.product-card .hover-image');
    hoverImages.forEach(img => {
      const preloadImg = new Image();
      preloadImg.src = img.src;
    });
  }

  // Initialize hover image preloading when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', preloadHoverImages);
  } else {
    preloadHoverImages();
  }
})();

// Product Detail Page Functionality
(function() {
  let variantsData = [];
  let selectedOptions = {};
  let currentVariant = null;

  // Initialize product detail functionality
  function initProductDetail() {
    // Check if we're on a product detail page
    if (!document.querySelector('.product-detail')) {
      return;
    }

    loadVariantsData();
    initThumbnailGallery();
    initProductOptions();
    initDetailTabs();
    // Don't call updateVariantDisplay() on init - let it show default images
  }

  // Load variants data from JSON script tag
  function loadVariantsData() {
    const dataScript = document.getElementById('product-variants-data');
    if (dataScript) {
      try {
        variantsData = JSON.parse(dataScript.textContent);
      } catch (e) {
        console.error('Failed to parse variants data:', e);
        variantsData = [];
      }
    }
  }

  // Initialize thumbnail gallery
  function initThumbnailGallery() {
    const thumbnails = document.querySelectorAll('.thumbnail');
    const mainImage = document.getElementById('main-product-image');

    thumbnails.forEach(thumbnail => {
      thumbnail.addEventListener('click', function() {
        // Remove active class from all thumbnails
        thumbnails.forEach(t => t.classList.remove('active'));

        // Add active class to clicked thumbnail
        this.classList.add('active');

        // Update main image
        const newImageSrc = this.getAttribute('data-image');
        if (mainImage && newImageSrc) {
          mainImage.src = newImageSrc;
        }
      });
    });
  }

  // Initialize product options (size and color)
  function initProductOptions() {
    const optionGroups = document.querySelectorAll('.option-group');

    optionGroups.forEach(group => {
      const optionType = group.getAttribute('data-option-type');
      const options = group.querySelectorAll('.size-option, .color-option');

      options.forEach(option => {
        option.addEventListener('click', function(e) {
          e.preventDefault();

          // Remove selected class from all options in this group
          options.forEach(opt => opt.classList.remove('selected'));

          // Add selected class to clicked option
          this.classList.add('selected');

          // Update selected options
          const valueId = this.getAttribute('data-value-id');
          selectedOptions[optionType] = parseInt(valueId);

          // Update variant display
          updateVariantDisplay();
        });
      });
    });
  }

  // Initialize detail tabs
  function initDetailTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
      button.addEventListener('click', function() {
        const targetTab = this.getAttribute('data-tab');

        // Remove active class from all buttons and panes
        tabButtons.forEach(btn => btn.classList.remove('active'));
        tabPanes.forEach(pane => pane.classList.remove('active'));

        // Add active class to clicked button and corresponding pane
        this.classList.add('active');
        const targetPane = document.getElementById(targetTab + '-tab');
        if (targetPane) {
          targetPane.classList.add('active');
        }
      });
    });
  }

  // Find variant based on selected options
  function findVariant(options) {
    // If no options are selected, return null
    if (Object.keys(options).length === 0) {
      return null;
    }

    // Get all available option types from variants data
    const availableOptionTypes = new Set();
    variantsData.forEach(variant => {
      Object.keys(variant.option_combination).forEach(optionType => {
        availableOptionTypes.add(optionType);
      });
    });

    // Only find variant if ALL required option types are selected
    const selectedOptionTypes = Object.keys(options);
    const hasAllRequiredOptions = Array.from(availableOptionTypes).every(optionType =>
      selectedOptionTypes.includes(optionType)
    );

    if (!hasAllRequiredOptions) {
      return null;
    }

    return variantsData.find(variant => {
      return Object.keys(options).every(optionType => {
        return variant.option_combination[optionType] === options[optionType];
      });
    });
  }

  // Update variant display based on selected options
  function updateVariantDisplay() {
    currentVariant = findVariant(selectedOptions);

    if (currentVariant) {
      // Check if variant has images
      if (currentVariant.images && currentVariant.images.length > 0) {
        updateVariantImages();
        disableHoverEffect();
      } else {
        // No images for this variant, show default images
        resetToDefaultImages();
        enableHoverEffect();
      }
      updateVariantSpecifications();
      updateVariantProductDetails();
    } else {
      resetToDefaultImages();
      resetToDefaultContent();
      enableHoverEffect();
    }
  }

  // Update variant images
  function updateVariantImages() {
    if (!currentVariant || !currentVariant.images || currentVariant.images.length === 0) {
      return;
    }

    const mainImage = document.getElementById('main-product-image');
    const thumbnailGallery = document.querySelector('.thumbnail-gallery');

    // Find primary image or use first image
    const primaryImage = currentVariant.images.find(img => img.is_primary) || currentVariant.images[0];

    // Update main image
    if (mainImage && primaryImage) {
      mainImage.src = primaryImage.url;
    }

    // Clear existing variant thumbnails (keep default thumbnails)
    const variantThumbnails = thumbnailGallery.querySelectorAll('.thumbnail.variant-image');
    variantThumbnails.forEach(thumb => thumb.remove());

    // Hide default thumbnails
    const defaultThumbnails = thumbnailGallery.querySelectorAll('.thumbnail.default-thumbnail');
    defaultThumbnails.forEach(thumb => thumb.style.display = 'none');

    // Add variant images as thumbnails
    currentVariant.images.forEach((image, index) => {
      const thumbnail = document.createElement('img');
      thumbnail.src = image.url;
      thumbnail.alt = 'Variant image';
      thumbnail.className = 'thumbnail variant-image';
      thumbnail.setAttribute('data-image', image.url);

      // Only mark as active if this is the primary image (which is currently displayed)
      if (image.url === primaryImage.url) {
        thumbnail.classList.add('active');
      }

      // Add click handler
      thumbnail.addEventListener('click', function() {
        document.querySelectorAll('.thumbnail').forEach(t => t.classList.remove('active'));
        this.classList.add('active');
        if (mainImage) {
          mainImage.src = this.getAttribute('data-image');
        }
      });

      thumbnailGallery.appendChild(thumbnail);
    });
  }

  // Update variant specifications
  function updateVariantSpecifications() {
    const variantSpecSection = document.getElementById('variant-specifications');
    const variantSpecList = document.getElementById('variant-spec-list');

    if (!variantSpecSection || !variantSpecList) return;

    // Clear existing variant specifications
    variantSpecList.innerHTML = '';

    if (currentVariant && currentVariant.specifications && currentVariant.specifications.length > 0) {
      // Add variant specifications
      currentVariant.specifications.forEach(spec => {
        const specItem = document.createElement('div');
        specItem.className = 'spec-item';
        specItem.innerHTML = `
          <span class="spec-key">${escapeHtml(spec.key)}</span>
          <span class="spec-value">${escapeHtml(spec.value)}</span>
        `;
        variantSpecList.appendChild(specItem);
      });

      // Show variant specifications section
      variantSpecSection.style.display = 'block';
    } else {
      // Hide variant specifications section if no specs
      variantSpecSection.style.display = 'none';
    }
  }

  // Reset to default images when no variant is selected
  function resetToDefaultImages() {
    const mainImage = document.getElementById('main-product-image');
    const thumbnailGallery = document.querySelector('.thumbnail-gallery');

    // Remove variant thumbnails
    const variantThumbnails = thumbnailGallery.querySelectorAll('.thumbnail.variant-image');
    variantThumbnails.forEach(thumb => thumb.remove());

    // Show default thumbnails
    const defaultThumbnails = thumbnailGallery.querySelectorAll('.thumbnail.default-thumbnail');
    defaultThumbnails.forEach(thumb => thumb.style.display = 'block');

    // Reset main image to cover image
    const coverThumbnail = thumbnailGallery.querySelector('.thumbnail.default-thumbnail');
    if (coverThumbnail && mainImage) {
      mainImage.src = coverThumbnail.getAttribute('data-image');
      // Reset active state
      thumbnailGallery.querySelectorAll('.thumbnail').forEach(t => t.classList.remove('active'));
      coverThumbnail.classList.add('active');
    }
  }

  // Update variant product details
  function updateVariantProductDetails() {
    const productDetailsTab = document.getElementById('details-tab');
    const defaultProductDetails = document.querySelector('[data-default-product-details]');

    if (productDetailsTab && currentVariant) {
      if (currentVariant.product_details && currentVariant.product_details.trim()) {
        // 变体有自定义产品详情，显示变体的详情
        productDetailsTab.innerHTML = `<p>${escapeHtml(currentVariant.product_details)}</p>`;
      } else if (defaultProductDetails) {
        // 变体没有自定义产品详情，显示默认的产品详情
        productDetailsTab.innerHTML = defaultProductDetails.innerHTML;
      }
    }
  }

  // Reset to default content when no variant is selected
  function resetToDefaultContent() {
    // Reset specifications to default (hide variant specs)
    const variantSpecSection = document.getElementById('variant-specifications');
    if (variantSpecSection) {
      variantSpecSection.style.display = 'none';
    }

    // Reset product details to default
    const productDetailsTab = document.getElementById('details-tab');
    const defaultProductDetails = document.querySelector('[data-default-product-details]');
    if (productDetailsTab && defaultProductDetails) {
      productDetailsTab.innerHTML = defaultProductDetails.innerHTML;
    }
  }

  // Disable hover effect when variant is selected
  function disableHoverEffect() {
    const mainImageContainer = document.querySelector('.main-image-container');
    if (mainImageContainer) {
      mainImageContainer.classList.add('variant-selected');
    }
  }

  // Enable hover effect when no variant is selected
  function enableHoverEffect() {
    const mainImageContainer = document.querySelector('.main-image-container');
    if (mainImageContainer) {
      mainImageContainer.classList.remove('variant-selected');
    }
  }

  // Utility function to escape HTML
  function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initProductDetail);
  } else {
    initProductDetail();
  }
})();

