/**
 * 图片懒加载实现
 * 使用Intersection Observer API实现高性能懒加载
 */

class LazyImageLoader {
    constructor() {
        this.imageObserver = null;
        this.init();
    }

    init() {
        // 检查浏览器是否支持Intersection Observer
        if ('IntersectionObserver' in window) {
            this.imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                // 提前50px开始加载
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            this.observeImages();
        } else {
            // 降级处理：直接加载所有图片
            this.loadAllImages();
        }
    }

    observeImages() {
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => {
            this.imageObserver.observe(img);
        });
    }

    loadImage(img) {
        // 创建新的Image对象预加载
        const imageLoader = new Image();
        
        imageLoader.onload = () => {
            // 图片加载完成后替换src
            img.src = img.dataset.src;
            
            // 如果有srcset，也要设置
            if (img.dataset.srcset) {
                img.srcset = img.dataset.srcset;
            }
            if (img.dataset.sizes) {
                img.sizes = img.dataset.sizes;
            }
            
            // 添加加载完成的类名
            img.classList.add('loaded');
            img.classList.remove('loading');
            
            // 清除data属性
            delete img.dataset.src;
            delete img.dataset.srcset;
            delete img.dataset.sizes;
        };
        
        imageLoader.onerror = () => {
            // 加载失败时的处理
            img.classList.add('error');
            img.classList.remove('loading');
        };
        
        // 开始加载
        img.classList.add('loading');
        imageLoader.src = img.dataset.src;
    }

    loadAllImages() {
        // 降级处理：直接加载所有图片
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => {
            this.loadImage(img);
        });
    }

    // 手动触发加载新添加的图片
    refresh() {
        if (this.imageObserver) {
            this.observeImages();
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.lazyLoader = new LazyImageLoader();
});

// 导出给其他脚本使用
window.LazyImageLoader = LazyImageLoader;
