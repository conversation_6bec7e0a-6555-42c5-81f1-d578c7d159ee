"""
图片处理工具函数
在上传时自动压缩和优化图片
"""
import os
from PIL import Image, ImageOps
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
import io


def compress_image(image_file, max_size=(1920, 1920), quality=85, format='WEBP'):
    """
    压缩图片文件
    
    Args:
        image_file: 上传的图片文件
        max_size: 最大尺寸 (width, height)
        quality: 压缩质量 (1-100)
        format: 输出格式 ('WEBP', 'JPEG', 'PNG')
    
    Returns:
        ContentFile: 压缩后的图片文件
    """
    try:
        # 打开图片
        image = Image.open(image_file)
        
        # 自动旋转图片（处理EXIF方向信息）
        image = ImageOps.exif_transpose(image)
        
        # 转换为RGB模式（WebP需要）
        if image.mode in ('RGBA', 'LA', 'P'):
            # 对于透明图片，创建白色背景
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'P':
                image = image.convert('RGBA')
            background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
            image = background
        elif image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 计算新尺寸（保持宽高比）
        original_width, original_height = image.size
        max_width, max_height = max_size
        
        # 如果图片小于最大尺寸，不需要缩放
        if original_width <= max_width and original_height <= max_height:
            new_width, new_height = original_width, original_height
        else:
            # 计算缩放比例
            width_ratio = max_width / original_width
            height_ratio = max_height / original_height
            ratio = min(width_ratio, height_ratio)
            
            new_width = int(original_width * ratio)
            new_height = int(original_height * ratio)
        
        # 缩放图片
        if (new_width, new_height) != (original_width, original_height):
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 保存到内存
        output = io.BytesIO()
        
        # 根据格式设置保存参数
        save_kwargs = {'format': format, 'optimize': True}
        if format in ['JPEG', 'WEBP']:
            save_kwargs['quality'] = quality
        elif format == 'PNG':
            save_kwargs['compress_level'] = 6
        
        image.save(output, **save_kwargs)
        output.seek(0)
        
        # 生成新的文件名
        original_name = os.path.splitext(image_file.name)[0]
        extension = '.webp' if format == 'WEBP' else f'.{format.lower()}'
        new_name = f"{original_name}_compressed{extension}"
        
        return ContentFile(output.getvalue(), name=new_name)
        
    except Exception as e:
        print(f"图片压缩失败: {e}")
        # 如果压缩失败，返回原文件
        return image_file


def get_image_info(image_file):
    """
    获取图片信息
    
    Args:
        image_file: 图片文件
        
    Returns:
        dict: 图片信息
    """
    try:
        image = Image.open(image_file)
        return {
            'width': image.width,
            'height': image.height,
            'format': image.format,
            'mode': image.mode,
            'size_bytes': image_file.size if hasattr(image_file, 'size') else 0
        }
    except Exception as e:
        print(f"获取图片信息失败: {e}")
        return {}


def should_compress_image(image_file, max_file_size=500*1024):  # 500KB
    """
    判断是否需要压缩图片

    Args:
        image_file: 图片文件
        max_file_size: 最大文件大小（字节）

    Returns:
        bool: 是否需要压缩
    """
    try:
        # 检查文件格式 - 如果不是WebP格式，就压缩
        if hasattr(image_file, 'name'):
            file_ext = image_file.name.lower().split('.')[-1]
            if file_ext in ['jpg', 'jpeg', 'png', 'bmp', 'tiff']:
                return True

        # 检查文件大小 - 降低阈值到500KB
        if hasattr(image_file, 'size') and image_file.size > max_file_size:
            return True

        # 检查图片尺寸 - 降低阈值
        image = Image.open(image_file)
        if image.width > 800 or image.height > 800:
            return True

        return False
    except Exception:
        return False


def optimize_uploaded_image(image_file, field_type='product'):
    """
    根据字段类型优化上传的图片
    
    Args:
        image_file: 上传的图片文件
        field_type: 字段类型 ('product', 'carousel', 'category', 'variant')
        
    Returns:
        ContentFile: 优化后的图片文件
    """
    # 根据不同类型设置不同的压缩参数
    compression_settings = {
        'product': {'max_size': (1200, 1200), 'quality': 90},
        'carousel': {'max_size': (1920, 1080), 'quality': 85},
        'category': {'max_size': (512, 512), 'quality': 85},
        'variant': {'max_size': (1000, 1000), 'quality': 90},
    }
    
    settings = compression_settings.get(field_type, {'max_size': (1200, 1200), 'quality': 85})
    
    # 只有当图片需要压缩时才进行处理
    if should_compress_image(image_file):
        return compress_image(
            image_file,
            max_size=settings['max_size'],
            quality=settings['quality'],
            format='WEBP'
        )
    else:
        return image_file
