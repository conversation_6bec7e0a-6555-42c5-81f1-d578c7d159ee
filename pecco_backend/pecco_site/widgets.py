from django import forms
from django.forms.widgets import ClearableFileInput
from django.utils.html import format_html
from django.utils.safestring import mark_safe


class PasteableImageWidget(forms.FileInput):
    """
    支持剪切板粘贴、拖拽和文件选择的图片上传Widget
    """

    class Media:
        css = {
            'all': ('admin/css/pasteable_image_widget.css',)
        }
        js = ('admin/js/pasteable_image_widget.js',)

    def __init__(self, attrs=None):
        default_attrs = {
            'class': 'pasteable-image-input',
            'accept': 'image/*',
            'style': 'display: none;'
        }
        if attrs:
            default_attrs.update(attrs)
        super().__init__(default_attrs)
    
    def render(self, name, value, attrs=None, renderer=None):
        """渲染widget HTML"""
        if attrs is None:
            attrs = {}

        # 获取当前图片URL
        current_image_url = ''
        if value and hasattr(value, 'url'):
            current_image_url = value.url

        # 生成唯一ID
        widget_id = attrs.get('id', f'id_{name}')

        # 先渲染原始的input元素
        original_input = super().render(name, value, attrs, renderer)

        # 构建自定义HTML
        html = f'''
<div class="pasteable-image-widget" data-field-name="{name}" id="{widget_id}_container" tabindex="0">
    <div class="image-upload-area" data-widget-id="{widget_id}">
        <div class="upload-instructions">
            <div class="upload-icon">📷</div>
            <div class="upload-text">
                <strong>上传图片</strong><br>
                <span>拖拽图片到此处</span><br>
                <span>或 <button type="button" class="browse-btn" data-target="{widget_id}">浏览文件</button></span><br>
                <span>或按 Ctrl+V 粘贴剪切板图片</span>
            </div>
        </div>
        <div class="image-preview">
            <img class="preview-img" src="" alt="预览">
            <div class="image-actions">
                <button type="button" class="remove-image-btn" data-target="{widget_id}">删除</button>
                <button type="button" class="change-image-btn" data-target="{widget_id}">更换</button>
            </div>
        </div>
    </div>
    {original_input}
    <div class="upload-status"></div>
</div>'''

        # 如果有现有图片，添加初始化脚本
        if current_image_url:
            # 检查图片文件是否真实存在
            import os
            from django.conf import settings

            # 构建完整的文件路径
            if hasattr(value, 'path'):
                try:
                    # 检查文件是否存在
                    file_exists = os.path.exists(value.path)
                except (ValueError, AttributeError):
                    file_exists = False
            else:
                file_exists = False

            if file_exists:
                html += f'''
                <script>
                    (function() {{
                        const container = document.getElementById('{widget_id}_container');
                        if (container) {{
                            const preview = container.querySelector('.image-preview');
                            const previewImg = container.querySelector('.preview-img');
                            const uploadArea = container.querySelector('.image-upload-area');

                            if (previewImg) {{
                                previewImg.src = '{current_image_url}';
                                preview.classList.add('show');
                                uploadArea.classList.add('has-image');
                            }}
                        }}
                    }})();
                </script>
                '''

        return mark_safe(html)
    

