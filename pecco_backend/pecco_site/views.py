from django.shortcuts import render, redirect, get_object_or_404
from django.conf import settings
from django.core.mail import send_mail
from django.contrib import messages
from django.utils.translation import get_language_from_request
from django.http import JsonResponse
from django.db.models import Q
import json
from .models import Category, Product, ProductTranslation, CarouselItem, CarouselTranslation, HomeLayoutBlock, NavigationItem, ContactMessage, StaticPage, ProductOption, ProductVariant, ProductSpecification
from .forms import ContactForm, ResellerForm, NewsletterForm


def get_locale(request):
    """
    获取当前语言设置，优先级：
    1. session中的语言设置
    2. 默认语言（英文）
    """
    # 首先检查session中的语言设置
    loc = request.session.get('locale')
    if loc and loc in ('zh','en','nl','fr','de'):
        return loc

    # 默认返回英文（不再检查浏览器语言偏好）
    default_lang = 'en'
    request.session['locale'] = default_lang
    return default_lang


def load_nav(locale: str, current_page: str = None):
    items = NavigationItem.objects.filter(is_active=True).order_by('sort_order')
    nav = []
    for it in items:
        t = it.translations.filter(locale=locale).first()
        label = t.label if t else it.label_key
        url = it.target
        if it.type == 'category':
            url = f"/products/?cat={it.target}"
        # ensure built-in pages route correctly even if seed data target differs
        if it.label_key == 'home':
            url = '/'
        if it.label_key == 'about':
            url = '/about/'
        if it.label_key == 'products':
            url = '/products/'
        if it.label_key == 'contact':
            url = '/contact/'

        # 判断是否为当前页面
        is_active = False
        if current_page == it.label_key:
            is_active = True
        elif current_page == 'home' and it.label_key == 'home':
            is_active = True

        nav.append({"label": label, "url": url, "is_active": is_active, "label_key": it.label_key})
    return nav


def home(request):
    locale = get_locale(request)
    blocks = HomeLayoutBlock.objects.filter(is_active=True).order_by('sort_order')

    # Prepare datasets; will render per blocks
    carousels = []
    cats = []
    featured_products = []

    if blocks.filter(block_type='carousel').exists():
        items = CarouselItem.objects.filter(is_active=True).order_by('sort_order')
        for item in items:
            t = item.translations.filter(locale=locale).first()

            # 使用优化的轮播图
            image_url = ''
            try:
                if item.image:
                    image_url = item.image_large.url if hasattr(item, 'image_large') else item.image.url
            except Exception:
                image_url = item.image.url if item.image else ''

            carousels.append({
                'image': image_url,
                'title': t.title if t else '',
                'subtitle': t.subtitle if t else '',
                'cta_text': t.cta_text if t else '',
                'link': item.link or '#'
            })

    if blocks.filter(block_type='categories').exists():
        cats = list(Category.objects.filter(is_active=True).order_by('sort_order'))

    if blocks.filter(block_type='products').exists():
        cfg = blocks.filter(block_type='products').first().config or {}
        tag = cfg.get('tag', 'featured')
        limit = int(cfg.get('limit', 8))
        qs = Product.objects.filter(is_active=True)
        if tag == 'featured':
            qs = qs.filter(tag_featured=True)
        elif tag == 'new':
            qs = qs.filter(tag_new=True)
        elif tag == 'hot':
            qs = qs.filter(tag_hot=True)
        for p in qs.order_by('sort_order')[:limit]:
            t = p.translations.filter(locale=locale).first()
            featured_products.append({
                'id': p.id,
                'name': t.name if t else f'Product {p.id}',
                'short_desc': t.short_desc if t else '',
                'cover': p.cover_image.url if p.cover_image else '',
                'hover': p.hover_image.url if p.hover_image else '',
                'tags': [tag for tag,flag in [('new',p.tag_new),('hot',p.tag_hot),('featured',p.tag_featured)] if flag]
            })



    context = {
        'locale': locale,
        'nav': load_nav(locale, 'home'),
        'carousels': carousels,
        'categories': cats,
        'featured_products': featured_products,
        'blocks': blocks,
        'slogan': 'Happy Paws, Happy Days',
        'breadcrumb': [],
    }
    return render(request, 'site/home.html', context)


def product_list(request):
    locale = get_locale(request)
    qs = Product.objects.filter(is_active=True)
    cat = request.GET.get('cat')
    if cat:
        qs = qs.filter(categories__slug=cat)
    products = qs.order_by('sort_order')

    items = []
    for p in products:
        t = p.translations.filter(locale=locale).first()

        # 使用优化的图片URL
        cover_url = ''
        hover_url = ''
        try:
            if p.cover_image:
                cover_url = p.cover_medium.url if hasattr(p, 'cover_medium') else p.cover_image.url
            if p.hover_image:
                hover_url = p.hover_medium.url if hasattr(p, 'hover_medium') else p.hover_image.url
        except Exception:
            # 如果优化图片生成失败，使用原图
            cover_url = p.cover_image.url if p.cover_image else ''
            hover_url = p.hover_image.url if p.hover_image else ''

        items.append({
            'id': p.id,
            'name': t.name if t else f'Product {p.id}',
            'short_desc': t.short_desc if t else '',
            'cover': cover_url,
            'hover': hover_url,
            'tags': [tag for tag,flag in [('new',p.tag_new),('hot',p.tag_hot),('featured',p.tag_featured)] if flag],
            'usage': p.usage,
        })

    # 获取所有分类信息
    categories = []
    for category in Category.objects.filter(is_active=True).order_by('sort_order'):
        trans = category.translations.filter(locale=locale).first()
        categories.append({
            'slug': category.slug,
            'name': trans.name if trans else category.slug.title(),
            'icon': category.icon.url if category.icon else '',
            'is_active': cat == category.slug
        })

    # 获取当前分类的名称（用于面包屑）
    current_category_name = None
    if cat:
        current_category = Category.objects.filter(slug=cat).first()
        if current_category:
            trans = current_category.translations.filter(locale=locale).first()
            current_category_name = trans.name if trans else cat.title()

    context = {
        'items': items,
        'categories': categories,
        'current_category': cat,
        'current_category_name': current_category_name,
        'nav': load_nav(locale, 'products'),
        'locale': locale,
        'slogan':'Happy Paws, Happy Days',
        'cat': cat,
        'breadcrumb': [{'label': ('Products' if locale=='en' else '产品' if locale=='zh' else 'Producten' if locale=='nl' else 'Produits' if locale=='fr' else 'Produkte'), 'url': '/products/'}] +
                      ([{'label': current_category_name or cat, 'url': None}] if cat else []),
    }
    return render(request, 'site/product_list.html', context)


def product_detail(request, pk: int):
    import json  # 在函数开头导入json模块
    locale = get_locale(request)
    p = get_object_or_404(Product, pk=pk, is_active=True)
    t = p.translations.filter(locale=locale).first()

    # 获取产品变体数据
    variants = p.variants.filter(is_active=True).prefetch_related(
        'option_values__option__translations',
        'option_values__translations',
        'images',
        'specifications__translations'
    ).order_by('sort_order')

    # 获取所有可用的选项类型
    available_options = {}
    for variant in variants:
        for option_value in variant.option_values.all():
            option_type = option_value.option.option_type
            if option_type not in available_options:
                available_options[option_type] = {
                    'option': option_value.option,
                    'values': set()
                }
            available_options[option_type]['values'].add(option_value)

    # 转换为列表并排序
    options_data = []
    for option_type, data in available_options.items():
        option = data['option']
        option_trans = option.translations.filter(locale=locale).first()

        values_list = []
        for value in sorted(data['values'], key=lambda x: x.sort_order):
            value_trans = value.translations.filter(locale=locale).first()
            values_list.append({
                'id': value.id,
                'value': value_trans.value if value_trans else value.value,
                'color_code': value.color_code,
                'original_value': value.value
            })

        options_data.append({
            'type': option_type,
            'name': option_trans.name if option_trans else option.name,
            'values': values_list
        })

    # 构建变体数据
    variants_data = []
    for variant in variants:
        variant_images = []
        for img in variant.images.order_by('sort_order'):
            # 使用优化的变体图片
            image_url = ''
            try:
                image_url = img.image_medium.url if hasattr(img, 'image_medium') else img.image.url
            except Exception:
                image_url = img.image.url

            variant_images.append({
                'url': image_url,
                'is_primary': img.is_primary
            })

        # 获取变体的选项组合
        option_combination = {}
        for option_value in variant.option_values.all():
            option_combination[option_value.option.option_type] = option_value.id

        # 获取变体规格
        specifications = []
        for spec in variant.specifications.filter(is_active=True).order_by('sort_order'):
            spec_trans = spec.translations.filter(locale=locale).first()
            specifications.append({
                'key': spec_trans.spec_key if spec_trans else spec.spec_key,
                'value': spec_trans.spec_value if spec_trans else spec.spec_value
            })

        variants_data.append({
            'id': variant.id,
            'sku': variant.sku,
            'option_combination': option_combination,
            'images': variant_images,
            'specifications': specifications,
            'product_details': variant.product_details or ''
        })

    # 获取产品通用规格
    general_specs = []

    # 1. 优先从ProductTranslation的general_specifications字段获取（JSON格式）
    if t and hasattr(t, 'general_specifications') and t.general_specifications is not None:
        # 如果general_specifications字段存在且不为None，则使用它（即使是空字符串）
        if t.general_specifications.strip():  # 只有当不是空字符串时才解析
            try:
                general_specs_data = json.loads(t.general_specifications)
                if isinstance(general_specs_data, dict):
                    for key, value in general_specs_data.items():
                        general_specs.append({
                            'key': key,
                            'value': value
                        })
            except (json.JSONDecodeError, TypeError, AttributeError):
                pass
        # 如果是空字符串，general_specs保持为空列表，不会回退到ProductSpecification
    else:
        # 2. 只有当ProductTranslation中没有general_specifications字段时，才从ProductSpecification模型获取
        for spec in p.specifications.filter(variant__isnull=True, is_active=True).order_by('sort_order'):
            spec_trans = spec.translations.filter(locale=locale).first()
            # 只有当找到对应语言的翻译时才添加规格
            if spec_trans and spec_trans.spec_key and spec_trans.spec_value:
                general_specs.append({
                    'key': spec_trans.spec_key,
                    'value': spec_trans.spec_value
                })

    # 获取默认产品详情（从翻译表）
    default_product_details = ''
    use_rich_desc_fallback = True  # 标记是否应该回退到rich_desc

    if t and hasattr(t, 'default_product_details') and t.default_product_details is not None:
        # 如果default_product_details字段存在且不为None，则使用它（即使是空字符串）
        default_product_details = t.default_product_details
        use_rich_desc_fallback = False  # 不回退到rich_desc
    else:
        # 只有当ProductTranslation中没有default_product_details字段时，才回退到rich_desc
        default_product_details = t.rich_desc if t else ''

    data = {
        'id': p.id,
        'name': t.name if t else f'Product {p.id}',
        'short_desc': t.short_desc if t else '',
        'rich_desc': t.rich_desc if t else '',
        'cover': p.cover_image.url if p.cover_image else '',
        'hover': p.hover_image.url if p.hover_image else '',

        'tags': [tag for tag,flag in [('new',p.tag_new),('hot',p.tag_hot),('featured',p.tag_featured)] if flag],
        'options': options_data,
        'variants': json.dumps(variants_data) if variants_data else '[]',  # 转换为JSON字符串，如果为空则返回空数组
        'general_specifications': general_specs,
        'default_product_details': default_product_details,
        'use_rich_desc_fallback': use_rich_desc_fallback
    }

    bc = [{'label': ('Products' if locale=='en' else '产品' if locale=='zh' else 'Producten' if locale=='nl' else 'Produits' if locale=='fr' else 'Produkte'), 'url': '/products/'}]
    bc.append({'label': data['name'], 'url': None})
    return render(request, 'site/product_detail.html', {'product': data, 'nav': load_nav(locale, 'products'), 'locale': locale, 'slogan':'Happy Paws, Happy Days', 'breadcrumb': bc})


def contact(request):
    locale = get_locale(request)
    if request.method == 'POST':
        form = ContactForm(request.POST)
        if form.is_valid():
            name = form.cleaned_data['name']
            email = form.cleaned_data['email']
            message = form.cleaned_data['message']

            # 保存到数据库
            contact_message = ContactMessage.objects.create(
                name=name,
                email=email,
                message=message
            )

            # 发送邮件
            try:
                subject = f"[Pecco Contact] {name}"
                body = f"From: {name} <{email}>\n\n{message}"
                send_mail(subject, body, settings.DEFAULT_FROM_EMAIL, [settings.CONTACT_RECIPIENT_EMAIL])
            except Exception as e:
                # 即使邮件发送失败，留言也已保存到数据库
                print(f"Email sending failed: {e}")

            messages.success(request, '已发送，我们会尽快与您联系。' if locale=='zh' else 'Verzonden! We nemen binnenkort contact met u op.' if locale=='nl' else 'Envoyé ! Nous vous répondrons bientôt.' if locale=='fr' else 'Gesendet! Wir melden uns bald bei Ihnen.' if locale=='de' else 'Sent! We will get back to you soon.')
            return redirect('contact')
    else:
        form = ContactForm()
    return render(request, 'site/contact.html', {'form': form, 'nav': load_nav(locale, 'contact'), 'locale': locale, 'slogan':'Happy Paws, Happy Days', 'breadcrumb':[{'label': ('Contact' if locale=='en' else '联系我们' if locale=='zh' else 'Contact' if locale=='nl' else 'Contact' if locale=='fr' else 'Kontakt'), 'url': None}]})


def about(request):
    locale = get_locale(request)
    
    # 从数据库获取About Us页面内容
    try:
        about_page = StaticPage.objects.get(page_type='about', is_active=True)
        translation = about_page.translations.filter(locale=locale).first()
        if not translation:
            # 如果没有找到对应语言的翻译，使用英语作为后备
            translation = about_page.translations.filter(locale='en').first()
        
        if translation:
            page_title = translation.title
            page_content = translation.content
        else:
            # 如果数据库中没有内容，使用默认内容
            page_title = 'About Us' if locale=='en' else '关于我们' if locale=='zh' else 'Over ons' if locale=='nl' else 'À propos' if locale=='fr' else 'Über uns'
            page_content = '<p>Content not available.</p>'
    except StaticPage.DoesNotExist:
        # 如果页面不存在，使用默认内容
        page_title = 'About Us' if locale=='en' else '关于我们' if locale=='zh' else 'Over ons' if locale=='nl' else 'À propos' if locale=='fr' else 'Über uns'
        page_content = '<p>Content not available.</p>'
    
    bc = [{'label': page_title, 'url': None}]
    return render(request, 'site/about.html', {
        'nav': load_nav(locale, 'about'),
        'locale': locale,
        'slogan': 'Happy Paws, Happy Days',
        'breadcrumb': bc,
        'page_title': page_title,
        'page_content': page_content,
    })


def privacy(request):
    locale = get_locale(request)
    
    # 使用静态内容，不再依赖数据库
    page_title = 'Privacy Policy' if locale=='en' else '隐私政策' if locale=='zh' else 'Privacybeleid' if locale=='nl' else 'Politique de confidentialité' if locale=='fr' else 'Datenschutz'
    
    bc = [{'label': page_title, 'url': None}]
    return render(request, 'site/privacy.html', {
        'nav': load_nav(locale, 'privacy'),
        'locale': locale,
        'slogan': 'Happy Paws, Happy Days',
        'breadcrumb': bc,
    })


def reseller_apply(request):
    """处理经销商申请"""
    locale = get_locale(request)
    if request.method == 'POST':
        form = ResellerForm(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']

            # 保存到数据库，使用默认消息
            default_message = '我想成为经销商，请及时与我联系。' if locale == 'zh' else 'Ik wil graag dealer worden, neem alstublieft snel contact met mij op.' if locale == 'nl' else 'Je souhaite devenir revendeur, veuillez me contacter rapidement.' if locale == 'fr' else 'Ich möchte gerne Händler werden, bitte kontaktieren Sie mich umgehend.' if locale == 'de' else 'I would like to become a reseller, please contact me promptly.'
            contact_message = ContactMessage.objects.create(
                name='Reseller Applicant',
                email=email,
                message=default_message
            )

            # 发送邮件
            try:
                subject = f"[Pecco Reseller] Application from {email}"
                body = f"New reseller application from: {email}\n\nMessage: {default_message}"
                send_mail(subject, body, settings.DEFAULT_FROM_EMAIL, [settings.CONTACT_RECIPIENT_EMAIL])
            except Exception as e:
                print(f"Email sending failed: {e}")

            messages.success(request, '申请已提交，我们会尽快与您联系。' if locale=='zh' else 'Aanvraag ingediend! We nemen binnenkort contact met u op.' if locale=='nl' else 'Demande soumise ! Nous vous contacterons bientôt.' if locale=='fr' else 'Antrag eingereicht! Wir melden uns bald bei Ihnen.' if locale=='de' else 'Application submitted! We will contact you soon.')
            return redirect('home')

    # 如果是GET请求或表单无效，重定向到首页
    return redirect('home')


def switch_lang(request, locale: str):
    if locale not in ('zh','en','nl','fr','de'):
        locale = 'en'
    request.session['locale'] = locale
    # 获取来源页面，如果没有则跳转到首页
    referer = request.META.get('HTTP_REFERER')
    if referer:
        # 从完整URL中提取路径部分
        from urllib.parse import urlparse
        parsed = urlparse(referer)
        return redirect(parsed.path)
    return redirect('home')


def debug_nav(request):
    """调试导航数据的视图"""
    locale = get_locale(request)
    nav_data = load_nav(locale, 'home')
    
    # 获取数据库中的原始数据
    nav_items = NavigationItem.objects.filter(is_active=True).order_by('sort_order')
    raw_data = []
    for item in nav_items:
        translations = item.translations.all()
        raw_data.append({
            'label_key': item.label_key,
            'target': item.target,
            'type': item.type,
            'translations': [(t.locale, t.label) for t in translations]
        })
    
    context = {
        'locale': locale,
        'nav_data': nav_data,
        'raw_data': raw_data,
        'session_locale': request.session.get('locale'),
        'all_session_data': dict(request.session)
    }
    
    return render(request, 'site/debug_nav.html', context)


def product_search(request):
    """产品搜索API"""
    query = request.GET.get('q', '').strip()
    # 优先使用URL参数中的locale，否则使用session中的locale
    locale = request.GET.get('locale') or get_locale(request)

    if not query or len(query) < 2:
        return JsonResponse({'results': []})

    # 搜索产品翻译表中的名称和描述
    translations = ProductTranslation.objects.filter(
        (Q(name__icontains=query) | Q(short_desc__icontains=query)) & Q(locale=locale),
        product__is_active=True
    ).select_related('product')[:10]

    results = []
    for trans in translations:
        product = trans.product
        results.append({
            'id': product.id,
            'name': trans.name,
            'short_desc': trans.short_desc,
            'cover': product.cover_image.url if product.cover_image else '',
            'url': f'/products/{product.id}/',
            'tags': [tag for tag, flag in [('new', product.tag_new), ('hot', product.tag_hot), ('featured', product.tag_featured)] if flag]
        })

    return JsonResponse({'results': results, 'locale': locale, 'query': query})


def newsletter_subscribe(request):
    """处理Newsletter订阅"""
    if request.method == 'POST':
        form = NewsletterForm(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']

            # 检查是否已经订阅过
            existing_subscription = ContactMessage.objects.filter(
                email=email,
                message_type='newsletter'
            ).first()

            if existing_subscription:
                return JsonResponse({
                    'success': False,
                    'message': 'This email is already subscribed to our newsletter.'
                })

            # 保存订阅信息
            ContactMessage.objects.create(
                message_type='newsletter',
                email=email,
                message='Newsletter subscription request'
            )

            return JsonResponse({
                'success': True,
                'message': 'Thank you for subscribing to our newsletter!'
            })
        else:
            return JsonResponse({
                'success': False,
                'message': 'Please enter a valid email address.'
            })

    return JsonResponse({'success': False, 'message': 'Invalid request method.'})


def test_hover(request):
    """测试悬停效果"""
    products = Product.objects.filter(hover_image__isnull=False)[:5]
    return render(request, 'site/test_hover.html', {'products': products})


def image_test(request):
    """图片优化测试页面"""
    products = Product.objects.filter(cover_image__isnull=False)[:5]
    return render(request, 'site/image_test.html', {'products': products})

