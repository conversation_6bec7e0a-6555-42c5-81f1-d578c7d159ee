from django.contrib import admin
from django_ckeditor_5.widgets import CKEditor5Widget
from django import forms
from django.utils.html import format_html
from django.db import models as django_models

# 自定义Admin站点配置
admin.site.site_header = 'PECCO 宠物商店管理后台'
admin.site.site_title = 'PECCO Admin'
admin.site.index_title = '欢迎使用 PECCO 管理系统'
from . import models
from .widgets import PasteableImageWidget

class TranslationInlineMixin:
    extra = 0

class CategoryTranslationInline(admin.TabularInline, TranslationInlineMixin):
    model = models.CategoryTranslation

@admin.register(models.Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('slug','sort_order','is_active','english_name','chinese_name','dutch_name','french_name','german_name')
    list_editable = ('sort_order','is_active')
    inlines = [CategoryTranslationInline]

    # 使用自定义的图片上传widget
    formfield_overrides = {
        django_models.ImageField: {'widget': PasteableImageWidget},
    }

    def english_name(self, obj):
        """显示英文名称"""
        trans = obj.translations.filter(locale='en').first()
        return trans.name if trans else '-'
    english_name.short_description = 'English'

    def chinese_name(self, obj):
        """显示中文名称"""
        trans = obj.translations.filter(locale='zh').first()
        return trans.name if trans else '-'
    chinese_name.short_description = '中文'

    def dutch_name(self, obj):
        """显示荷兰语名称"""
        trans = obj.translations.filter(locale='nl').first()
        return trans.name if trans else '-'
    dutch_name.short_description = 'Nederlands'

    def french_name(self, obj):
        """显示法语名称"""
        trans = obj.translations.filter(locale='fr').first()
        return trans.name if trans else '-'
    french_name.short_description = 'Français'

    def german_name(self, obj):
        """显示德语名称"""
        trans = obj.translations.filter(locale='de').first()
        return trans.name if trans else '-'
    german_name.short_description = 'Deutsch'

class ProductTranslationInline(admin.TabularInline, TranslationInlineMixin):
    model = models.ProductTranslation
    fields = ('locale', 'name', 'short_desc', 'rich_desc', 'default_product_details', 'general_specifications')

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)

        # 自定义表单以改善字段显示
        class CustomForm(formset.form):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, **kwargs)
                # 设置字段的widget属性
                if 'default_product_details' in self.fields:
                    self.fields['default_product_details'].widget.attrs.update({
                        'rows': 3,
                        'placeholder': '当变体没有自定义产品详情时显示的默认内容'
                    })
                if 'general_specifications' in self.fields:
                    self.fields['general_specifications'].widget.attrs.update({
                        'rows': 3,
                        'placeholder': 'JSON格式：{"Brand": "PECCO", "Type": "Insect Repellent Kit"}'
                    })

        formset.form = CustomForm
        return formset


class ProductVariantInline(admin.TabularInline):
    model = models.ProductVariant
    fields = ('option_values', 'sku', 'sort_order', 'is_active')
    filter_horizontal = ('option_values',)
    extra = 0


@admin.register(models.Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('id','english_name','chinese_name','dutch_name','french_name','german_name','categories_display','variants_count','sort_order','is_active','tag_new','tag_hot','tag_featured')
    list_editable = ('sort_order','is_active','tag_new','tag_hot','tag_featured')
    fields = ('categories', 'cover_image', 'hover_image', 'usage', 'sort_order', 'is_active', 'tag_new', 'tag_hot', 'tag_featured')
    inlines = [ProductTranslationInline]  # 移除ProductImageInline和ProductVariantInline，避免重复管理
    filter_horizontal = ('categories',)

    # 使用自定义的图片上传widget
    formfield_overrides = {
        django_models.ImageField: {'widget': PasteableImageWidget},
    }

    def english_name(self, obj):
        """显示英文名称"""
        trans = obj.translations.filter(locale='en').first()
        return trans.name if trans else f'Product {obj.id}'
    english_name.short_description = 'English'

    def chinese_name(self, obj):
        """显示中文名称"""
        trans = obj.translations.filter(locale='zh').first()
        return trans.name if trans else f'Product {obj.id}'
    chinese_name.short_description = '中文'

    def dutch_name(self, obj):
        """显示荷兰语名称"""
        trans = obj.translations.filter(locale='nl').first()
        return trans.name if trans else f'Product {obj.id}'
    dutch_name.short_description = 'Nederlands'

    def french_name(self, obj):
        """显示法语名称"""
        trans = obj.translations.filter(locale='fr').first()
        return trans.name if trans else f'Product {obj.id}'
    french_name.short_description = 'Français'

    def german_name(self, obj):
        """显示德语名称"""
        trans = obj.translations.filter(locale='de').first()
        return trans.name if trans else f'Product {obj.id}'
    german_name.short_description = 'Deutsch'

    def categories_display(self, obj):
        """显示所有分类"""
        return ', '.join([cat.slug for cat in obj.categories.all()])
    categories_display.short_description = '分类'

    def variants_count(self, obj):
        """显示变体数量"""
        return obj.variants.count()
    variants_count.short_description = '变体数量'

class CarouselTranslationInline(admin.TabularInline, TranslationInlineMixin):
    model = models.CarouselTranslation
    fields = ('locale', 'title', 'subtitle', 'cta_text')

@admin.register(models.CarouselItem)
class CarouselAdmin(admin.ModelAdmin):
    list_display = ('id','sort_order','is_active','english_title','chinese_title','dutch_title','french_title','german_title')
    list_editable = ('sort_order','is_active')
    fields = ('image', 'link', 'sort_order', 'is_active')
    inlines = [CarouselTranslationInline]

    # 使用自定义的图片上传widget
    formfield_overrides = {
        django_models.ImageField: {'widget': PasteableImageWidget},
    }

    def english_title(self, obj):
        """显示英文标题"""
        trans = obj.translations.filter(locale='en').first()
        return trans.title if trans else '-'
    english_title.short_description = 'English Title'

    def chinese_title(self, obj):
        """显示中文标题"""
        trans = obj.translations.filter(locale='zh').first()
        return trans.title if trans else '-'
    chinese_title.short_description = '中文标题'

    def dutch_title(self, obj):
        """显示荷兰语标题"""
        trans = obj.translations.filter(locale='nl').first()
        return trans.title if trans else '-'
    dutch_title.short_description = 'Nederlandse Titel'

    def french_title(self, obj):
        """显示法语标题"""
        trans = obj.translations.filter(locale='fr').first()
        return trans.title if trans else '-'
    french_title.short_description = 'Titre Français'

    def german_title(self, obj):
        """显示德语标题"""
        trans = obj.translations.filter(locale='de').first()
        return trans.title if trans else '-'
    german_title.short_description = 'Deutscher Titel'



class NavigationTranslationInline(admin.TabularInline, TranslationInlineMixin):
    model = models.NavigationTranslation

@admin.register(models.NavigationItem)
class NavigationItemAdmin(admin.ModelAdmin):
    list_display = ('label_key','english_label','chinese_label','dutch_label','french_label','german_label','type','target','is_active','sort_order')
    list_editable = ('type','target','is_active','sort_order')
    inlines = [NavigationTranslationInline]
    ordering = ('sort_order',)

    def english_label(self, obj):
        """显示英文标签"""
        trans = obj.translations.filter(locale='en').first()
        return trans.label if trans else '-'
    english_label.short_description = 'English'

    def chinese_label(self, obj):
        """显示中文标签"""
        trans = obj.translations.filter(locale='zh').first()
        return trans.label if trans else '-'
    chinese_label.short_description = '中文'

    def dutch_label(self, obj):
        """显示荷兰语标签"""
        trans = obj.translations.filter(locale='nl').first()
        return trans.label if trans else '-'
    dutch_label.short_description = 'Nederlands'

    def french_label(self, obj):
        """显示法语标签"""
        trans = obj.translations.filter(locale='fr').first()
        return trans.label if trans else '-'
    french_label.short_description = 'Français'

    def german_label(self, obj):
        """显示德语标签"""
        trans = obj.translations.filter(locale='de').first()
        return trans.label if trans else '-'
    german_label.short_description = 'Deutsch'

class HomeLayoutBlockTranslationInline(admin.TabularInline, TranslationInlineMixin):
    model = models.HomeLayoutBlockTranslation

@admin.register(models.HomeLayoutBlock)
class HomeLayoutBlockAdmin(admin.ModelAdmin):
    list_display = ('block_type','english_name','chinese_name','dutch_name','french_name','german_name','is_active','sort_order')
    list_editable = ('is_active','sort_order')
    inlines = [HomeLayoutBlockTranslationInline]

    def english_name(self, obj):
        """显示英文名称"""
        trans = obj.translations.filter(locale='en').first()
        return trans.title if trans else '-'
    english_name.short_description = 'English'

    def chinese_name(self, obj):
        """显示中文名称"""
        trans = obj.translations.filter(locale='zh').first()
        return trans.title if trans else '-'
    chinese_name.short_description = '中文'

    def dutch_name(self, obj):
        """显示荷兰语名称"""
        trans = obj.translations.filter(locale='nl').first()
        return trans.title if trans else '-'
    dutch_name.short_description = 'Nederlands'

    def french_name(self, obj):
        """显示法语名称"""
        trans = obj.translations.filter(locale='fr').first()
        return trans.title if trans else '-'
    french_name.short_description = 'Français'

    def german_name(self, obj):
        """显示德语名称"""
        trans = obj.translations.filter(locale='de').first()
        return trans.title if trans else '-'
    german_name.short_description = 'Deutsch'

@admin.register(models.ContactMessage)
class ContactMessageAdmin(admin.ModelAdmin):
    list_display = ('message_type', 'display_name', 'email', 'created_at', 'is_read', 'replied_at')
    list_filter = ('message_type', 'is_read', 'created_at')
    search_fields = ('name', 'email', 'message')
    readonly_fields = ('created_at', 'updated_at')
    list_editable = ('is_read',)
    ordering = ('-created_at',)

    fieldsets = (
        ('基本信息', {
            'fields': ('message_type', 'name', 'email', 'created_at')
        }),
        ('留言内容', {
            'fields': ('message',)
        }),
        ('处理状态', {
            'fields': ('is_read', 'replied_at')
        }),
    )

    def display_name(self, obj):
        """显示姓名，如果为空则显示邮箱"""
        return obj.name if obj.name else obj.email
    display_name.short_description = '姓名/邮箱'

    def has_add_permission(self, request):
        # 不允许在后台手动添加留言
        return False

class StaticPageTranslationForm(forms.ModelForm):
    content = forms.CharField(widget=CKEditor5Widget(attrs={"class": "django_ckeditor_5"}, config_name='extends'))

    class Meta:
        model = models.StaticPageTranslation
        fields = '__all__'

class StaticPageTranslationInline(admin.TabularInline, TranslationInlineMixin):
    model = models.StaticPageTranslation
    form = StaticPageTranslationForm
    fields = ('locale', 'title', 'content', 'meta_title', 'meta_description')

    class Media:
        css = {
            'all': ('css/admin_custom.css',)
        }

@admin.register(models.StaticPage)
class StaticPageAdmin(admin.ModelAdmin):
    list_display = ('page_type', 'is_active', 'english_title', 'chinese_title', 'dutch_title', 'french_title', 'german_title')
    list_editable = ('is_active',)
    inlines = [StaticPageTranslationInline]
    fieldsets = (
        ('基本信息', {
            'fields': ('page_type', 'is_active')
        }),
        ('SEO设置', {
            'fields': ('meta_title', 'meta_description'),
            'classes': ('collapse',)
        }),
    )

    def english_title(self, obj):
        """显示英文标题"""
        trans = obj.translations.filter(locale='en').first()
        return trans.title if trans else '-'
    english_title.short_description = 'English Title'

    def chinese_title(self, obj):
        """显示中文标题"""
        trans = obj.translations.filter(locale='zh').first()
        return trans.title if trans else '-'
    chinese_title.short_description = '中文标题'

    def dutch_title(self, obj):
        """显示荷兰语标题"""
        trans = obj.translations.filter(locale='nl').first()
        return trans.title if trans else '-'
    dutch_title.short_description = 'Nederlandse Titel'

    def french_title(self, obj):
        """显示法语标题"""
        trans = obj.translations.filter(locale='fr').first()
        return trans.title if trans else '-'
    french_title.short_description = 'Titre Français'

    def german_title(self, obj):
        """显示德语标题"""
        trans = obj.translations.filter(locale='de').first()
        return trans.title if trans else '-'
    german_title.short_description = 'Deutscher Titel'


# Product Variant Admin
class ProductOptionTranslationInline(admin.TabularInline, TranslationInlineMixin):
    model = models.ProductOptionTranslation
    fields = ('locale', 'name')


class ProductOptionValueTranslationInline(admin.TabularInline, TranslationInlineMixin):
    model = models.ProductOptionValueTranslation
    fields = ('locale', 'value')


class ProductOptionValueInline(admin.TabularInline):
    model = models.ProductOptionValue
    fields = ('value', 'color_code', 'sort_order', 'is_active')
    extra = 0


@admin.register(models.ProductOption)
class ProductOptionAdmin(admin.ModelAdmin):
    list_display = ('name', 'option_type', 'sort_order', 'is_active', 'values_count')
    list_editable = ('sort_order', 'is_active')
    list_filter = ('option_type', 'is_active')
    fields = ('name', 'option_type', 'sort_order', 'is_active')
    inlines = [ProductOptionValueInline, ProductOptionTranslationInline]

    def values_count(self, obj):
        return obj.values.count()
    values_count.short_description = '选项值数量'


@admin.register(models.ProductOptionValue)
class ProductOptionValueAdmin(admin.ModelAdmin):
    list_display = ('option', 'value', 'color_preview', 'sort_order', 'is_active')
    list_editable = ('sort_order', 'is_active')
    list_filter = ('option', 'is_active')
    fields = ('option', 'value', 'color_code', 'sort_order', 'is_active')
    inlines = [ProductOptionValueTranslationInline]

    def color_preview(self, obj):
        if obj.color_code:
            return format_html(
                '<div style="width: 20px; height: 20px; background-color: {}; border: 1px solid #ccc; border-radius: 3px;"></div>',
                obj.color_code
            )
        return '-'
    color_preview.short_description = '颜色预览'


class ProductVariantImageInline(admin.TabularInline):
    model = models.ProductVariantImage
    fields = ('image', 'sort_order', 'is_primary')
    extra = 0

    # 使用自定义的图片上传widget
    formfield_overrides = {
        django_models.ImageField: {'widget': PasteableImageWidget},
    }

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)

        class CustomFormSet(formset):
            def clean(self):
                super().clean()
                if any(self.errors):
                    return

                # 检查主图设置
                primary_count = 0
                has_images = False

                for form in self.forms:
                    if form.cleaned_data and not form.cleaned_data.get('DELETE', False):
                        if form.cleaned_data.get('image'):
                            has_images = True
                            if form.cleaned_data.get('is_primary'):
                                primary_count += 1

                if has_images and primary_count == 0:
                    raise forms.ValidationError('如果有图片，必须设置一张主图。')
                elif primary_count > 1:
                    raise forms.ValidationError('只能设置一张主图。')

        return CustomFormSet


class ProductSpecificationInline(admin.TabularInline):
    model = models.ProductSpecification
    fields = ('spec_key', 'spec_value', 'sort_order', 'is_active')
    extra = 1
    verbose_name = '变体规格'
    verbose_name_plural = '变体规格 (在此处维护该变体的所有规格信息)'

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        # 自动设置product字段，避免用户手动选择
        if db_field.name == "product":
            kwargs["widget"] = forms.HiddenInput()
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)

        # 自定义表单以自动填充product和variant字段
        class CustomForm(formset.form):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, **kwargs)
                if obj:  # obj是ProductVariant实例
                    # 自动设置product和variant字段的初始值
                    if 'product' in self.fields:
                        self.fields['product'].initial = obj.product
                        self.fields['product'].widget = forms.HiddenInput()
                    if 'variant' in self.fields:
                        self.fields['variant'].initial = obj
                        self.fields['variant'].widget = forms.HiddenInput()

            def save(self, commit=True):
                instance = super().save(commit=False)
                if obj:  # 确保product和variant字段被正确设置
                    instance.product = obj.product
                    instance.variant = obj
                if commit:
                    instance.save()
                return instance

        formset.form = CustomForm
        return formset

    class Media:
        css = {
            'all': ('admin/css/custom_inline.css',)
        }
        js = ('admin/js/specification_helper.js',)


class ProductSpecificationTranslationInline(admin.TabularInline, TranslationInlineMixin):
    model = models.ProductSpecificationTranslation
    fields = ('locale', 'spec_key', 'spec_value')


@admin.register(models.ProductVariant)
class ProductVariantAdmin(admin.ModelAdmin):
    list_display = ('product', 'option_combination_display', 'sku', 'specifications_preview', 'is_active', 'images_count')
    list_editable = ('sku', 'is_active')
    list_filter = ('product', 'is_active')
    fields = ('product', 'option_values', 'sku', 'product_details', 'sort_order', 'is_active')
    filter_horizontal = ('option_values',)
    inlines = [ProductVariantImageInline, ProductSpecificationInline]

    def option_combination_display(self, obj):
        values = obj.option_values.all()
        if values:
            return " + ".join([f"{v.option.name}: {v.value}" for v in values])
        return '-'
    option_combination_display.short_description = '选项组合'

    def specifications_preview(self, obj):
        specs = obj.specifications.all()[:3]  # 显示前3个规格
        if specs:
            preview = ", ".join([f"{s.spec_key}: {s.spec_value[:20]}..." if len(s.spec_value) > 20 else f"{s.spec_key}: {s.spec_value}" for s in specs])
            if obj.specifications.count() > 3:
                preview += f" (+{obj.specifications.count() - 3} more)"
            return preview
        return '-'
    specifications_preview.short_description = '规格预览'

    def images_count(self, obj):
        return obj.images.count()
    images_count.short_description = '图片数量'


# 注释掉独立的ProductSpecification管理 - 应该通过ProductVariant来管理
# @admin.register(models.ProductSpecification)
class ProductSpecificationAdmin(admin.ModelAdmin):
    """
    产品规格管理 - 已禁用独立管理
    规格应该通过产品变体(ProductVariant)来管理，避免重复维护
    """
    list_display = ('product', 'variant', 'spec_key', 'spec_value_preview', 'sort_order', 'is_active')
    list_editable = ('sort_order', 'is_active')
    list_filter = ('product', 'is_active')
    fields = ('product', 'variant', 'spec_key', 'spec_value', 'sort_order', 'is_active')
    inlines = [ProductSpecificationTranslationInline]

    def spec_value_preview(self, obj):
        if len(obj.spec_value) > 50:
            return obj.spec_value[:50] + '...'
        return obj.spec_value
    spec_value_preview.short_description = '规格值'

