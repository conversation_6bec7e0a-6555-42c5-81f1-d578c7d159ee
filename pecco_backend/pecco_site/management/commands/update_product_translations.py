from django.core.management.base import BaseCommand
from pecco_site.models import *
import json


class Command(BaseCommand):
    help = '更新产品的多语言通用规格翻译并增加产品规格'

    def handle(self, *args, **options):
        self.stdout.write('开始更新产品翻译和规格...')
        
        # 产品类型和目标的多语言翻译
        product_translations = {
            6: {  # 星球包
                'type': {'zh': '宠物背包', 'en': 'Pet Carrier', 'de': 'Haustiertasche', 'nl': 'Huisdier Drager', 'fr': 'Sac de Transport'},
                'target': {'zh': '小型宠物', 'en': 'Small Pets', 'de': 'Kleine Haustiere', 'nl': 'Kleine Huisdieren', 'fr': 'Petits Animaux'},
                'extra_specs': ['Ventilation System', 'Comfort Padding']
            },
            7: {  # 咕噜包
                'type': {'zh': '透明背包', 'en': 'Transparent Carrier', 'de': 'Transparente Tasche', 'nl': 'Transparante Drager', 'fr': 'Sac Transparent'},
                'target': {'zh': '小型宠物', 'en': 'Small Pets', 'de': 'Kleine Haustiere', 'nl': 'Kleine Huisdieren', 'fr': 'Petits Animaux'},
                'extra_specs': ['360° Visibility', 'Anti-Scratch Material']
            },
            8: {  # 肩包航空箱
                'type': {'zh': '航空箱', 'en': 'Airline Carrier', 'de': 'Flugbox', 'nl': 'Vliegtuig Drager', 'fr': 'Caisse de Transport Aérien'},
                'target': {'zh': '中小型宠物', 'en': 'Small to Medium Pets', 'de': 'Kleine bis mittlere Haustiere', 'nl': 'Kleine tot Middelgrote Huisdieren', 'fr': 'Petits à Moyens Animaux'},
                'extra_specs': ['IATA Certification', 'Reinforced Structure']
            },
            9: {  # 塑料袋拾便器
                'type': {'zh': '清洁用品', 'en': 'Cleaning Accessory', 'de': 'Reinigungszubehör', 'nl': 'Schoonmaak Accessoire', 'fr': 'Accessoire de Nettoyage'},
                'target': {'zh': '所有宠物', 'en': 'All Pets', 'de': 'Alle Haustiere', 'nl': 'Alle Huisdieren', 'fr': 'Tous les Animaux'},
                'extra_specs': ['Eco-Friendly', 'Easy Dispensing']
            },
            10: {  # 驱虫项圈
                'type': {'zh': '驱虫用品', 'en': 'Pest Control', 'de': 'Schädlingsbekämpfung', 'nl': 'Ongediertebestrijding', 'fr': 'Lutte Antiparasitaire'},
                'target': {'zh': '犬猫通用', 'en': 'Dogs & Cats', 'de': 'Hunde & Katzen', 'nl': 'Honden & Katten', 'fr': 'Chiens & Chats'},
                'extra_specs': ['Natural Ingredients', 'Long-lasting Protection']
            },
            12: {  # 训练套装
                'type': {'zh': '训练用品', 'en': 'Training Kit', 'de': 'Trainingsset', 'nl': 'Trainingsset', 'fr': 'Kit d\'Entraînement'},
                'target': {'zh': '幼犬幼猫', 'en': 'Puppies & Kittens', 'de': 'Welpen & Kätzchen', 'nl': 'Puppy\'s & Kittens', 'fr': 'Chiots & Chatons'},
                'extra_specs': ['Training Manual', 'Progressive Difficulty']
            },
            13: {  # 饮水机
                'type': {'zh': '饮水设备', 'en': 'Water Fountain', 'de': 'Wasserspender', 'nl': 'Waterfontein', 'fr': 'Fontaine à Eau'},
                'target': {'zh': '犬猫通用', 'en': 'Dogs & Cats', 'de': 'Hunde & Katzen', 'nl': 'Honden & Katten', 'fr': 'Chiens & Chats'},
                'extra_specs': ['Silent Motor', 'LED Indicator']
            },
            14: {  # 窝垫
                'type': {'zh': '宠物床垫', 'en': 'Pet Bed', 'de': 'Haustierbett', 'nl': 'Huisdierbed', 'fr': 'Lit pour Animaux'},
                'target': {'zh': '中小型宠物', 'en': 'Small to Medium Pets', 'de': 'Kleine bis mittlere Haustiere', 'nl': 'Kleine tot Middelgrote Huisdieren', 'fr': 'Petits à Moyens Animaux'},
                'extra_specs': ['Memory Foam', 'Removable Cover']
            },
            15: {  # 长毛绒圆形
                'type': {'zh': '圆形床垫', 'en': 'Round Bed', 'de': 'Rundes Bett', 'nl': 'Rond Bed', 'fr': 'Lit Rond'},
                'target': {'zh': '小型宠物', 'en': 'Small Pets', 'de': 'Kleine Haustiere', 'nl': 'Kleine Huisdieren', 'fr': 'Petits Animaux'},
                'extra_specs': ['Ultra Soft Plush', 'Calming Design']
            },
            16: {  # 短毛绒圆形
                'type': {'zh': '圆形床垫', 'en': 'Round Bed', 'de': 'Rundes Bett', 'nl': 'Rond Bed', 'fr': 'Lit Rond'},
                'target': {'zh': '小型宠物', 'en': 'Small Pets', 'de': 'Kleine Haustiere', 'nl': 'Kleine Huisdieren', 'fr': 'Petits Animaux'},
                'extra_specs': ['Breathable Fabric', 'Summer Comfort']
            },
            17: {  # 玫瑰绒垫子
                'type': {'zh': '绒面垫子', 'en': 'Velvet Cushion', 'de': 'Samt-Kissen', 'nl': 'Fluweel Kussen', 'fr': 'Coussin en Velours'},
                'target': {'zh': '中型宠物', 'en': 'Medium Pets', 'de': 'Mittlere Haustiere', 'nl': 'Middelgrote Huisdieren', 'fr': 'Animaux Moyens'},
                'extra_specs': ['Luxury Material', 'Elegant Design']
            },
            18: {  # 灯芯绒垫子
                'type': {'zh': '灯芯绒垫子', 'en': 'Corduroy Cushion', 'de': 'Cord-Kissen', 'nl': 'Corduroy Kussen', 'fr': 'Coussin en Velours Côtelé'},
                'target': {'zh': '中型宠物', 'en': 'Medium Pets', 'de': 'Mittlere Haustiere', 'nl': 'Middelgrote Huisdieren', 'fr': 'Animaux Moyens'},
                'extra_specs': ['Vintage Style', 'Durable Fabric']
            },
            19: {  # 防水毯子
                'type': {'zh': '防水毯子', 'en': 'Waterproof Blanket', 'de': 'Wasserdichte Decke', 'nl': 'Waterdichte Deken', 'fr': 'Couverture Imperméable'},
                'target': {'zh': '大型宠物', 'en': 'Large Pets', 'de': 'Große Haustiere', 'nl': 'Grote Huisdieren', 'fr': 'Grands Animaux'},
                'extra_specs': ['Outdoor Use', 'Double-sided Design']
            },
            20: {  # 双层猫砂盆
                'type': {'zh': '猫砂盆', 'en': 'Litter Box', 'de': 'Katzenklo', 'nl': 'Kattenbak', 'fr': 'Bac à Litière'},
                'target': {'zh': '猫咪专用', 'en': 'Cats Only', 'de': 'Nur für Katzen', 'nl': 'Alleen voor Katten', 'fr': 'Chats Uniquement'},
                'extra_specs': ['Double Layer System', 'Odor Lock Technology']
            },
            21: {  # 浮毛收集器
                'type': {'zh': '清洁工具', 'en': 'Grooming Tool', 'de': 'Pflegewerkzeug', 'nl': 'Verzorgingsgereedschap', 'fr': 'Outil de Toilettage'},
                'target': {'zh': '长毛宠物', 'en': 'Long-haired Pets', 'de': 'Langhaarige Haustiere', 'nl': 'Langharige Huisdieren', 'fr': 'Animaux à Poils Longs'},
                'extra_specs': ['Floating Design', 'Reusable Filter']
            },
            22: {  # 家具
                'type': {'zh': '宠物家具', 'en': 'Pet Furniture', 'de': 'Haustiermöbel', 'nl': 'Huisdier Meubels', 'fr': 'Mobilier pour Animaux'},
                'target': {'zh': '室内宠物', 'en': 'Indoor Pets', 'de': 'Haustiere für Innenräume', 'nl': 'Binnen Huisdieren', 'fr': 'Animaux d\'Intérieur'},
                'extra_specs': ['Multi-functional', 'Modern Design']
            },
            23: {  # 恐龙猫爬架
                'type': {'zh': '猫爬架', 'en': 'Cat Tree', 'de': 'Katzenbaum', 'nl': 'Kattenboom', 'fr': 'Arbre à Chat'},
                'target': {'zh': '猫咪专用', 'en': 'Cats Only', 'de': 'Nur für Katzen', 'nl': 'Alleen voor Katten', 'fr': 'Chats Uniquement'},
                'extra_specs': ['Dinosaur Theme', 'Multi-level Platform']
            },
            24: {  # 猫抓板猫屋
                'type': {'zh': '猫抓板', 'en': 'Cat Scratcher', 'de': 'Kratzbrett', 'nl': 'Krabpaal', 'fr': 'Griffoir'},
                'target': {'zh': '猫咪专用', 'en': 'Cats Only', 'de': 'Nur für Katzen', 'nl': 'Alleen voor Katten', 'fr': 'Chats Uniquement'},
                'extra_specs': ['2-in-1 Design', 'Furniture Protection']
            },
            25: {  # 猫窗户吊床
                'type': {'zh': '窗户吊床', 'en': 'Window Hammock', 'de': 'Fensterhängematte', 'nl': 'Raam Hangmat', 'fr': 'Hamac de Fenêtre'},
                'target': {'zh': '猫咪专用', 'en': 'Cats Only', 'de': 'Nur für Katzen', 'nl': 'Alleen voor Katten', 'fr': 'Chats Uniquement'},
                'extra_specs': ['Suction Cup Mount', 'Sunbathing Spot']
            },
            26: {  # 润足膏
                'type': {'zh': '护理用品', 'en': 'Care Product', 'de': 'Pflegeprodukt', 'nl': 'Verzorgingsproduct', 'fr': 'Produit de Soin'},
                'target': {'zh': '犬猫通用', 'en': 'Dogs & Cats', 'de': 'Hunde & Katzen', 'nl': 'Honden & Katten', 'fr': 'Chiens & Chats'},
                'extra_specs': ['Vitamin E Formula', 'Healing Properties']
            },
            27: {  # 免洗泡沫
                'type': {'zh': '清洁用品', 'en': 'Cleaning Product', 'de': 'Reinigungsprodukt', 'nl': 'Schoonmaakproduct', 'fr': 'Produit de Nettoyage'},
                'target': {'zh': '所有宠物', 'en': 'All Pets', 'de': 'Alle Haustiere', 'nl': 'Alle Huisdieren', 'fr': 'Tous les Animaux'},
                'extra_specs': ['No-rinse Formula', 'Travel Size']
            },
            28: {  # 吸毛器
                'type': {'zh': '除毛工具', 'en': 'Hair Remover', 'de': 'Haarentferner', 'nl': 'Haar Verwijderaar', 'fr': 'Éliminateur de Poils'},
                'target': {'zh': '长毛宠物', 'en': 'Long-haired Pets', 'de': 'Langhaarige Haustiere', 'nl': 'Langharige Huisdieren', 'fr': 'Animaux à Poils Longs'},
                'extra_specs': ['Powerful Suction', 'Multiple Attachments']
            }
        }
        
        self.update_translations(product_translations)
        self.add_extra_specifications(product_translations)
        
        self.stdout.write(self.style.SUCCESS('产品翻译和规格更新完成！'))

    def update_translations(self, product_translations):
        """更新产品翻译"""
        for product_id, data in product_translations.items():
            try:
                product = Product.objects.get(id=product_id)
                self.stdout.write(f'更新产品 ID {product_id} 的翻译')
                
                for locale in ['zh', 'en', 'de', 'nl', 'fr']:
                    translation = product.translations.filter(locale=locale).first()
                    if translation:
                        # 创建多语言通用规格
                        general_specs = {
                            '类型' if locale == 'zh' else ('Typ' if locale == 'de' else ('Type' if locale in ['en', 'nl', 'fr'] else 'Type')): data['type'][locale],
                            '适用对象' if locale == 'zh' else ('Zielgruppe' if locale == 'de' else ('Doelgroep' if locale == 'nl' else ('Cible' if locale == 'fr' else 'Target'))): data['target'][locale]
                        }
                        
                        translation.general_specifications = json.dumps(general_specs, ensure_ascii=False)
                        translation.save()
                        
            except Product.DoesNotExist:
                self.stdout.write(f'产品 ID {product_id} 不存在，跳过')

    def add_extra_specifications(self, product_translations):
        """为产品变体添加额外规格"""
        for product_id, data in product_translations.items():
            try:
                product = Product.objects.get(id=product_id)
                variants = product.variants.all()
                
                for variant in variants:
                    for spec_name in data['extra_specs']:
                        # 检查规格是否已存在
                        if not variant.specifications.filter(spec_key=spec_name).exists():
                            spec_value = self.generate_spec_value(spec_name, variant)
                            
                            ProductSpecification.objects.create(
                                product=product,
                                variant=variant,
                                spec_key=spec_name,
                                spec_value=spec_value,
                                sort_order=variant.specifications.count(),
                                is_active=True
                            )
                            
            except Product.DoesNotExist:
                continue

    def generate_spec_value(self, spec_name, variant):
        """根据规格名称生成对应的值"""
        spec_values = {
            'Ventilation System': 'Advanced air circulation',
            'Comfort Padding': 'Extra soft padding',
            '360° Visibility': 'Full panoramic view',
            'Anti-Scratch Material': 'Scratch-resistant surface',
            'IATA Certification': 'Airline approved',
            'Reinforced Structure': 'Extra strong frame',
            'Eco-Friendly': 'Biodegradable materials',
            'Easy Dispensing': 'One-hand operation',
            'Natural Ingredients': '100% natural formula',
            'Long-lasting Protection': 'Up to 8 months',
            'Training Manual': 'Step-by-step guide included',
            'Progressive Difficulty': 'Beginner to advanced',
            'Silent Motor': 'Ultra-quiet operation',
            'LED Indicator': 'Water level display',
            'Memory Foam': 'Pressure-relieving foam',
            'Removable Cover': 'Machine washable',
            'Ultra Soft Plush': 'Premium plush material',
            'Calming Design': 'Anxiety-reducing shape',
            'Breathable Fabric': 'Temperature regulating',
            'Summer Comfort': 'Cool and comfortable',
            'Luxury Material': 'Premium velvet finish',
            'Elegant Design': 'Sophisticated styling',
            'Vintage Style': 'Classic retro look',
            'Durable Fabric': 'Long-lasting material',
            'Outdoor Use': 'Weather resistant',
            'Double-sided Design': 'Waterproof/warming sides',
            'Double Layer System': 'Efficient waste separation',
            'Odor Lock Technology': 'Advanced odor control',
            'Floating Design': 'Automatic collection',
            'Reusable Filter': 'Washable and reusable',
            'Multi-functional': 'Rest, play, and storage',
            'Modern Design': 'Contemporary styling',
            'Dinosaur Theme': 'Fun prehistoric design',
            'Multi-level Platform': 'Multiple climbing levels',
            '2-in-1 Design': 'Scratcher and house combo',
            'Furniture Protection': 'Saves your furniture',
            'Suction Cup Mount': 'Strong window attachment',
            'Sunbathing Spot': 'Perfect for sunny windows',
            'Vitamin E Formula': 'Nourishing and healing',
            'Healing Properties': 'Repairs cracked pads',
            'No-rinse Formula': 'Convenient waterless cleaning',
            'Travel Size': 'Perfect for on-the-go',
            'Powerful Suction': 'High-efficiency motor',
            'Multiple Attachments': 'Various brush heads'
        }
        
        return spec_values.get(spec_name, f'High-quality {spec_name.lower()}')
