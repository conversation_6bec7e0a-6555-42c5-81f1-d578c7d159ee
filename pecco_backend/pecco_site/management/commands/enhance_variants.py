from django.core.management.base import BaseCommand
from pecco_site.models import *
import random


class Command(BaseCommand):
    help = 'Enhance product variant descriptions and add new variants'

    def handle(self, *args, **options):
        self.stdout.write('Starting to enhance product variants...')

        # First enhance existing variants' product details
        self.enhance_all_variants()

        # Then add new variants for each product
        self.add_new_variants()

        self.stdout.write(self.style.SUCCESS('Product variant enhancement completed!'))

    def enhance_all_variants(self):
        """Enhance all variants' product details with English descriptions"""
        self.stdout.write('Enhancing all variant descriptions...')

        # Product detail templates in English
        product_details_templates = {
            'Pet Carrier': {
                'base': "🌟 The perfect companion for cosmic adventures! This space-themed pet carrier brings you and your beloved pet an unprecedented travel experience.",
                'features': [
                    "✨ Unique planetary pattern design makes your pet the brightest star on the street",
                    "🌬️ 360-degree breathable mesh system ensures comfortable breathing for pets",
                    "🛡️ Thickened shock-absorbing bottom pad protects pet's spinal health",
                    "🔒 Safety lock design prevents accidental opening",
                    "👜 Ergonomic shoulder straps reduce owner's burden"
                ]
            },
            'Transparent Carrier': {
                'base': "🔮 Transparent world, unlimited vision! Let your pet enjoy a 360-degree panoramic sightseeing experience.",
                'features': [
                    "🌈 Crystal-clear transparent design allows pets to observe surroundings",
                    "💎 High-strength PC material, impact-resistant and scratch-proof",
                    "🌪️ Turbine ventilation system for more efficient air circulation",
                    "🎨 Replaceable decorative stickers for personalized customization",
                    "🔧 Quick-release cleaning design for super easy maintenance"
                ]
            },
            'Airline Carrier': {
                'base': "✈️ Flight-level safety guarantee! Professional pet transport box complying with international aviation standards.",
                'features': [
                    "🛫 IATA International Air Transport Association certified",
                    "🏗️ Aviation-grade aluminum alloy frame with super strong load-bearing",
                    "🌊 Waterproof and moisture-proof design adapts to various weather conditions",
                    "📏 Precise size design meets requirements of major airlines",
                    "🔐 TSA-approved locks for worry-free security checks"
                ]
            },
            'Cleaning Accessory': {
                'base': "🌱 Eco-friendly travel companion! Making dog walking more civilized and convenient.",
                'features': [
                    "♻️ Biodegradable materials, environmentally friendly and pollution-free",
                    "🎯 One-touch bag dispensing design for single-handed operation",
                    "🧲 Strong magnetic base can attach to metal surfaces",
                    "🎒 Lightweight design for burden-free carrying",
                    "🌿 Mint-scented bags with natural deodorizing properties"
                ]
            },
            'Pest Control': {
                'base': "🛡️ Natural protection shield! 8-month long-lasting protection keeps pests away from your beloved pet.",
                'features': [
                    "🌿 100% natural plant essential oil formula",
                    "⏰ 8-month ultra-long protection period",
                    "💧 Waterproof design, unaffected by swimming or bathing",
                    "🔄 Slow-release technology for continuous and stable release",
                    "👶 Gentle and non-irritating, safe for young pets"
                ]
            }
        }
        
        # Generate detailed descriptions for all existing variants
        variants = ProductVariant.objects.all()

        # Map product names to template keys
        product_template_map = {
            '星球包': 'Pet Carrier',
            '咕噜包': 'Transparent Carrier',
            '肩包航空箱': 'Airline Carrier',
            '塑料袋拾便器': 'Cleaning Accessory',
            '驱虫项圈': 'Pest Control'
        }

        for variant in variants:
            product_name = variant.product.get_chinese_name()
            template_key = product_template_map.get(product_name)

            if template_key and template_key in product_details_templates:
                template = product_details_templates[template_key]

                # Get variant option combination
                option_combo = variant.get_option_combination()
                size_value = option_combo.get('size')
                color_value = option_combo.get('color')

                # Generate personalized description
                size_text = size_value.value if size_value else "Standard Size"
                color_text = color_value.value if color_value else "Classic Color"

                detailed_description = f"{template['base']}\n\n"
                detailed_description += f"🎨 **{color_text} Edition - {size_text}**\n"
                detailed_description += f"This {color_text} color perfectly showcases the product's elegant temperament, while the {size_text} design is more suitable for your pet's body type.\n\n"
                detailed_description += "**Product Highlights:**\n"

                # Randomly select 3-4 featured functions
                selected_features = random.sample(template['features'], min(4, len(template['features'])))
                for feature in selected_features:
                    detailed_description += f"{feature}\n"

                detailed_description += f"\n💝 Choose the {color_text} {size_text} edition for the most caring protection for your pet!"

                # Update variant description
                variant.product_details = detailed_description
                variant.save()

                self.stdout.write(f'Updated description for variant {variant.id}')
            else:
                # For variants without specific templates, add basic English description
                if not variant.product_details or any(chinese_char in variant.product_details for chinese_char in ['这款', '产品', '宠物']):
                    basic_details = f"High-quality {product_name} designed for your beloved pet.\n\n"
                    basic_details += "🎯 Product Features:\n"
                    basic_details += "✨ Premium materials and craftsmanship\n"
                    basic_details += "🛡️ Safe and comfortable for pets\n"
                    basic_details += "🎨 Stylish and functional design\n"
                    basic_details += "💝 Perfect gift for pet lovers\n"

                    # Add specifications if available
                    option_combo = variant.get_option_combination()
                    size_value = option_combo.get('size')
                    color_value = option_combo.get('color')

                    if size_value or color_value:
                        basic_details += f"\n📋 Current Specifications:\n"
                        if size_value:
                            basic_details += f"📏 Size: {size_value.value}\n"
                        if color_value:
                            basic_details += f"🎨 Color: {color_value.value}\n"

                    variant.product_details = basic_details
                    variant.save()

                    self.stdout.write(f'Added basic English details for variant {variant.id}')

    def add_new_variants(self):
        """Add new variants for each product"""
        self.stdout.write('Adding new variants for each product...')

        # Get all available option values
        size_options = list(ProductOptionValue.objects.filter(option__option_type='size'))
        color_options = list(ProductOptionValue.objects.filter(option__option_type='color'))

        if not size_options or not color_options:
            self.stdout.write('Missing option values, cannot create new variants')
            return

        # Add a new random variant for each product
        for product in Product.objects.all():
            existing_variants = product.variants.all()
            existing_combinations = set()

            # Collect existing combinations
            for variant in existing_variants:
                combo = variant.get_option_combination()
                size_id = combo.get('size').id if combo.get('size') else None
                color_id = combo.get('color').id if combo.get('color') else None
                existing_combinations.add((size_id, color_id))

            # Generate new random combinations
            max_attempts = 20
            for attempt in range(max_attempts):
                random_size = random.choice(size_options)
                random_color = random.choice(color_options)
                new_combination = (random_size.id, random_color.id)

                if new_combination not in existing_combinations:
                    # Create new variant
                    new_variant = ProductVariant.objects.create(
                        product=product,
                        sku=f'{product.id}-{random_size.value}-{random_color.value}-NEW',
                        product_details=self.generate_variant_description(product, random_size, random_color),
                        is_active=True,
                        sort_order=existing_variants.count()
                    )

                    # Add option values
                    new_variant.option_values.add(random_size, random_color)

                    # Create specifications for new variant
                    self.create_variant_specifications(new_variant, random_size, random_color)

                    self.stdout.write(f'Added new variant for product {product.id}: {random_size.value} + {random_color.value}')
                    break
            else:
                self.stdout.write(f'Product {product.id} cannot find new combinations')

    def generate_variant_description(self, product, size, color):
        """Generate description for new variants"""
        product_name = product.get_chinese_name()

        # Basic description templates
        base_templates = {
            '星球包': f"🌟 Brand new {color.value} {size.value} space carrier, creating an exclusive cosmic space for your pet!",
            '咕噜包': f"🔮 {color.value} transparent {size.value} bubble carrier, let your pet enjoy a wonderful journey with panoramic views!",
            '肩包航空箱': f"✈️ {color.value} professional {size.value} airline carrier, internationally certified, the first choice for air travel!",
            '塑料袋拾便器': f"🌱 {color.value} eco-friendly {size.value} waste bag dispenser, an essential tool for civilized pet ownership!",
            '驱虫项圈': f"🛡️ {color.value} natural {size.value} pest control collar, 8-month long-lasting protection!",
            '训练套装': f"🎓 {color.value} professional {size.value} training set, scientific training starts here!",
            '饮水机': f"💧 {color.value} smart {size.value} water fountain, making pets love drinking water!",
            '窝垫': f"🛏️ {color.value} comfortable {size.value} pet bed, giving pets a five-star sleeping experience!"
        }

        # Find matching template
        description = None
        for key, template in base_templates.items():
            if key in product_name:
                description = template
                break

        if not description:
            description = f"✨ Premium {color.value} {size.value} {product_name}, providing the highest quality experience for your beloved pet!"

        # Add general feature descriptions
        features = [
            f"🎨 **{color.value} Color Design**: Fashionable and beautiful, showcasing taste",
            f"📏 **{size.value} Specification**: Carefully designed, perfect fit",
            "🏆 **Premium Materials**: Safe, eco-friendly, and durable",
            "💝 **Thoughtful Design**: Attention to detail, convenient to use",
            "🌟 **Quality Assurance**: Strict quality control, trustworthy"
        ]

        selected_features = random.sample(features, 3)
        description += "\n\n**Product Features:**\n"
        for feature in selected_features:
            description += f"{feature}\n"

        description += f"\n🎁 Choose this {color.value} {size.value} version to give your pet exclusive customized care!"

        return description

    def create_variant_specifications(self, variant, size, color):
        """Create specifications for new variants"""
        # Basic specifications
        base_specs = {
            'Weight': f'{random.uniform(1.0, 6.0):.1f} kg',
            'Dimensions': f'{size.value}',
            'Material': f'Premium {color.value} Material',
            'Color': f'{color.value}',
            'Size': f'{size.value}',
            'Durability': 'High-grade construction'
        }

        # Add specific specifications based on product type
        product_name = variant.product.get_chinese_name()

        if '饮水机' in product_name:
            base_specs.update({
                'Capacity': f'{random.randint(1, 4)}L',
                'Power': f'{random.randint(5, 20)}W',
                'Noise Level': '< 35dB'
            })
        elif '项圈' in product_name:
            base_specs.update({
                'Protection Duration': '8 months',
                'Waterproof': 'Yes',
                'Adjustable Range': f'{random.randint(20, 30)}-{random.randint(60, 80)}cm'
            })
        elif '背包' in product_name or '航空箱' in product_name:
            base_specs.update({
                'Max Pet Weight': f'{random.randint(3, 10)} kg',
                'Ventilation': '360° air circulation',
                'Safety Features': 'Multiple safety locks'
            })

        # Create specification records (select 4-5 specifications)
        selected_specs = dict(random.sample(list(base_specs.items()), min(5, len(base_specs))))

        for i, (key, value) in enumerate(selected_specs.items()):
            ProductSpecification.objects.create(
                product=variant.product,
                variant=variant,
                spec_key=key,
                spec_value=value,
                sort_order=i,
                is_active=True
            )
