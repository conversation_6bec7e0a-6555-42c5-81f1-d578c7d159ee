"""
生成优化图片的管理命令
为现有的图片生成不同尺寸和格式的优化版本
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from pecco_site.models import Product, CarouselItem, Category, ProductVariantImage
import os


class Command(BaseCommand):
    help = '为现有图片生成优化版本'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制重新生成所有优化图片',
        )
        parser.add_argument(
            '--model',
            type=str,
            choices=['product', 'carousel', 'category', 'variant', 'all'],
            default='all',
            help='指定要处理的模型类型',
        )

    def handle(self, *args, **options):
        force = options['force']
        model_type = options['model']
        
        self.stdout.write('开始生成优化图片...')
        
        if model_type in ['product', 'all']:
            self.process_products(force)
            
        if model_type in ['carousel', 'all']:
            self.process_carousels(force)
            
        if model_type in ['category', 'all']:
            self.process_categories(force)
            
        if model_type in ['variant', 'all']:
            self.process_variants(force)
        
        self.stdout.write(self.style.SUCCESS('优化图片生成完成！'))

    def process_products(self, force=False):
        """处理产品图片"""
        self.stdout.write('处理产品图片...')
        
        products = Product.objects.filter(cover_image__isnull=False)
        total = products.count()
        
        for i, product in enumerate(products, 1):
            self.stdout.write(f'处理产品 {i}/{total}: {product}')
            
            try:
                # 触发优化图片生成
                if product.cover_image:
                    _ = product.cover_thumbnail.url
                    _ = product.cover_medium.url
                    _ = product.cover_large.url
                    
                if product.hover_image:
                    _ = product.hover_thumbnail.url
                    _ = product.hover_medium.url
                    _ = product.hover_large.url
                    
                self.stdout.write(f'  ✓ 产品 {product} 图片优化完成')
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'  ✗ 产品 {product} 图片优化失败: {e}')
                )

    def process_carousels(self, force=False):
        """处理轮播图"""
        self.stdout.write('处理轮播图...')
        
        carousels = CarouselItem.objects.filter(image__isnull=False)
        total = carousels.count()
        
        for i, carousel in enumerate(carousels, 1):
            self.stdout.write(f'处理轮播图 {i}/{total}: {carousel}')
            
            try:
                # 触发优化图片生成
                if carousel.image:
                    _ = carousel.image_thumbnail.url
                    _ = carousel.image_medium.url
                    _ = carousel.image_large.url
                    
                self.stdout.write(f'  ✓ 轮播图 {carousel} 图片优化完成')
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'  ✗ 轮播图 {carousel} 图片优化失败: {e}')
                )

    def process_categories(self, force=False):
        """处理分类图标"""
        self.stdout.write('处理分类图标...')
        
        categories = Category.objects.filter(icon__isnull=False)
        total = categories.count()
        
        for i, category in enumerate(categories, 1):
            self.stdout.write(f'处理分类 {i}/{total}: {category}')
            
            try:
                # 触发优化图片生成
                if category.icon:
                    _ = category.icon_small.url
                    _ = category.icon_medium.url
                    
                self.stdout.write(f'  ✓ 分类 {category} 图标优化完成')
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'  ✗ 分类 {category} 图标优化失败: {e}')
                )

    def process_variants(self, force=False):
        """处理产品变体图片"""
        self.stdout.write('处理产品变体图片...')
        
        variants = ProductVariantImage.objects.filter(image__isnull=False)
        total = variants.count()
        
        for i, variant_image in enumerate(variants, 1):
            self.stdout.write(f'处理变体图片 {i}/{total}: {variant_image}')
            
            try:
                # 触发优化图片生成
                if variant_image.image:
                    _ = variant_image.image_thumbnail.url
                    _ = variant_image.image_medium.url
                    _ = variant_image.image_large.url
                    
                self.stdout.write(f'  ✓ 变体图片 {variant_image} 优化完成')
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'  ✗ 变体图片 {variant_image} 优化失败: {e}')
                )
