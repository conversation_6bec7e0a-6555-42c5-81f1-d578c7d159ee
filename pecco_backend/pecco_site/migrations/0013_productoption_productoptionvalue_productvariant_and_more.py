# Generated by Django 4.2.13 on 2025-08-26 07:17

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('pecco_site', '0012_product_hover_image'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductOption',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=50, verbose_name='选项名称')),
                ('option_type', models.CharField(choices=[('size', '规格'), ('color', '颜色'), ('material', '材质'), ('other', '其他')], max_length=20, verbose_name='选项类型')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='排序')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '产品选项类型',
                'verbose_name_plural': '产品选项类型',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ProductOptionValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('value', models.CharField(max_length=100, verbose_name='选项值')),
                ('color_code', models.CharField(blank=True, help_text='颜色选项的十六进制颜色代码，如：#FF0000', max_length=7, null=True, verbose_name='颜色代码')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='排序')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('option', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='values', to='pecco_site.productoption')),
            ],
            options={
                'verbose_name': '产品选项值',
                'verbose_name_plural': '产品选项值',
                'ordering': ['option', 'sort_order', 'value'],
                'unique_together': {('option', 'value')},
            },
        ),
        migrations.CreateModel(
            name='ProductVariant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('sku', models.CharField(blank=True, max_length=100, null=True, verbose_name='SKU编码')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='排序')),
                ('option_values', models.ManyToManyField(to='pecco_site.productoptionvalue', verbose_name='选项值组合')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='variants', to='pecco_site.product')),
            ],
            options={
                'verbose_name': '产品变体',
                'verbose_name_plural': '产品变体',
                'ordering': ['product', 'sort_order'],
            },
        ),
        migrations.CreateModel(
            name='ProductVariantImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='product_variants/', verbose_name='变体图片')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='排序')),
                ('is_primary', models.BooleanField(default=False, verbose_name='是否为主图')),
                ('variant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='pecco_site.productvariant')),
            ],
            options={
                'verbose_name': '产品变体图片',
                'verbose_name_plural': '产品变体图片',
                'ordering': ['variant', 'sort_order'],
            },
        ),
        migrations.CreateModel(
            name='ProductSpecification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('spec_key', models.CharField(max_length=100, verbose_name='规格键')),
                ('spec_value', models.TextField(verbose_name='规格值')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='排序')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否显示')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='specifications', to='pecco_site.product')),
                ('variant', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='specifications', to='pecco_site.productvariant', verbose_name='关联变体')),
            ],
            options={
                'verbose_name': '产品规格',
                'verbose_name_plural': '产品规格',
                'ordering': ['product', 'variant', 'sort_order'],
            },
        ),
        migrations.CreateModel(
            name='ProductSpecificationTranslation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('locale', models.CharField(choices=[('zh', '中文'), ('en', 'English'), ('de', 'Deutsch'), ('nl', 'Nederlands'), ('fr', 'Français')], max_length=5)),
                ('spec_key', models.CharField(max_length=100, verbose_name='规格键')),
                ('spec_value', models.TextField(verbose_name='规格值')),
                ('specification', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='pecco_site.productspecification')),
            ],
            options={
                'verbose_name': '规格翻译',
                'verbose_name_plural': '规格翻译',
                'unique_together': {('specification', 'locale')},
            },
        ),
        migrations.CreateModel(
            name='ProductOptionValueTranslation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('locale', models.CharField(choices=[('zh', '中文'), ('en', 'English'), ('de', 'Deutsch'), ('nl', 'Nederlands'), ('fr', 'Français')], max_length=5)),
                ('value', models.CharField(max_length=100, verbose_name='选项值')),
                ('option_value', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='pecco_site.productoptionvalue')),
            ],
            options={
                'verbose_name': '选项值翻译',
                'verbose_name_plural': '选项值翻译',
                'unique_together': {('option_value', 'locale')},
            },
        ),
        migrations.CreateModel(
            name='ProductOptionTranslation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('locale', models.CharField(choices=[('zh', '中文'), ('en', 'English'), ('de', 'Deutsch'), ('nl', 'Nederlands'), ('fr', 'Français')], max_length=5)),
                ('name', models.CharField(max_length=50, verbose_name='选项名称')),
                ('option', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='pecco_site.productoption')),
            ],
            options={
                'verbose_name': '选项类型翻译',
                'verbose_name_plural': '选项类型翻译',
                'unique_together': {('option', 'locale')},
            },
        ),
    ]
