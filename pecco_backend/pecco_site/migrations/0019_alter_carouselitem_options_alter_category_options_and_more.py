# Generated by Django 4.2.13 on 2025-08-27 01:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pecco_site', '0018_remove_product_default_product_details_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='carouselitem',
            options={'ordering': ['sort_order'], 'verbose_name': '轮播图', 'verbose_name_plural': '轮播图'},
        ),
        migrations.AlterModelOptions(
            name='category',
            options={'ordering': ['sort_order', 'slug'], 'verbose_name': '产品分类', 'verbose_name_plural': '产品分类'},
        ),
        migrations.AlterModelOptions(
            name='product',
            options={'ordering': ['sort_order', '-created_at'], 'verbose_name': '产品', 'verbose_name_plural': '产品'},
        ),
        migrations.AlterModelOptions(
            name='productimage',
            options={'ordering': ['product', 'sort_order'], 'verbose_name': '产品图片', 'verbose_name_plural': '产品图片'},
        ),
        migrations.AlterModelOptions(
            name='productvariant',
            options={'ordering': ['product', 'sort_order'], 'verbose_name': '产品规格', 'verbose_name_plural': '产品规格'},
        ),
        migrations.AlterModelOptions(
            name='productvariantimage',
            options={'ordering': ['variant', 'sort_order'], 'verbose_name': '产品规格图片', 'verbose_name_plural': '产品规格图片'},
        ),
        migrations.AlterField(
            model_name='productvariantimage',
            name='image',
            field=models.ImageField(upload_to='product_variants/', verbose_name='规格图片'),
        ),
    ]
