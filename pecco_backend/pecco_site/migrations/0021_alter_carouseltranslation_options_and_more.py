# Generated by Django 4.2.13 on 2025-08-27 03:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pecco_site', '0020_alter_homelayoutblock_options_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='carouseltranslation',
            options={'verbose_name': '轮播图翻译', 'verbose_name_plural': '轮播图翻译'},
        ),
        migrations.AlterField(
            model_name='carouseltranslation',
            name='cta_text',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='按钮文字'),
        ),
        migrations.AlterField(
            model_name='carouseltranslation',
            name='locale',
            field=models.CharField(choices=[('zh', '中文'), ('en', 'English'), ('de', 'Deutsch'), ('nl', 'Nederlands'), ('fr', 'Français')], max_length=5, verbose_name='语言'),
        ),
        migrations.AlterField(
            model_name='carouseltranslation',
            name='subtitle',
            field=models.CharField(blank=True, default='', max_length=300, verbose_name='副标题'),
        ),
        migrations.AlterField(
            model_name='carouseltranslation',
            name='title',
            field=models.CharField(blank=True, default='', max_length=200, verbose_name='标题'),
        ),
    ]
