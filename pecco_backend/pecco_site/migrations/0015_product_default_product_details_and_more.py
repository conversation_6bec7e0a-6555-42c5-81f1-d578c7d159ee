# Generated by Django 4.2.13 on 2025-08-26 09:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pecco_site', '0014_productvariant_product_details'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='default_product_details',
            field=models.TextField(blank=True, help_text='当变体没有自定义产品详情时显示的默认内容', null=True, verbose_name='默认产品详情'),
        ),
        migrations.AddField(
            model_name='product',
            name='general_specifications',
            field=models.TextField(blank=True, help_text='适用于所有变体的通用规格信息，JSON格式：{"Weight Range": "2-6 kg", "Material": "Premium Cotton"}', null=True, verbose_name='通用规格'),
        ),
    ]
