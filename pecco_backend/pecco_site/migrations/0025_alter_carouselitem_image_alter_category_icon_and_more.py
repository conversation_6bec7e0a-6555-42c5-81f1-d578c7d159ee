# Generated by Django 4.2.13 on 2025-08-27 06:35

from django.db import migrations
import pecco_site.fields


class Migration(migrations.Migration):

    dependencies = [
        ('pecco_site', '0024_update_variant_verbose_names'),
    ]

    operations = [
        migrations.AlterField(
            model_name='carouselitem',
            name='image',
            field=pecco_site.fields.CarouselImageField(upload_to='carousels/'),
        ),
        migrations.AlterField(
            model_name='category',
            name='icon',
            field=pecco_site.fields.CategoryImageField(blank=True, null=True, upload_to='category_icons/'),
        ),
        migrations.AlterField(
            model_name='product',
            name='cover_image',
            field=pecco_site.fields.ProductImageField(upload_to='product_covers/'),
        ),
        migrations.AlterField(
            model_name='product',
            name='hover_image',
            field=pecco_site.fields.ProductImageField(blank=True, help_text='悬停时显示的第二张图片（可选）', null=True, upload_to='product_covers/'),
        ),
        migrations.AlterField(
            model_name='productvariantimage',
            name='image',
            field=pecco_site.fields.VariantImageField(upload_to='product_variants/', verbose_name='变体图片'),
        ),
    ]
