{% load i18n %}
<div class="pasteable-image-widget" data-field-name="{{ widget.name }}" id="{{ widget.attrs.id }}_container">
    <div class="image-upload-area" id="{{ widget.attrs.id }}_upload_area">
        <div class="upload-instructions">
            <div class="upload-icon">📷</div>
            <div class="upload-text">
                <strong>上传图片</strong><br>
                <span>拖拽图片到此处</span><br>
                <span>或 <button type="button" class="browse-btn">浏览文件</button></span><br>
                <span>或按 Ctrl+V 粘贴剪切板图片</span>
            </div>
        </div>
        <div class="image-preview" id="{{ widget.attrs.id }}_preview" style="display: none;">
            <img id="{{ widget.attrs.id }}_preview_img" src="" alt="预览">
            <div class="image-actions">
                <button type="button" class="remove-image-btn">删除</button>
                <button type="button" class="change-image-btn">更换</button>
            </div>
        </div>
    </div>
    
    {% include "django/forms/widgets/input.html" %}
    
    <div class="upload-status" id="{{ widget.attrs.id }}_status"></div>
</div>

{% if widget.value %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const container = document.getElementById('{{ widget.attrs.id }}_container');
    const preview = container.querySelector('.image-preview');
    const previewImg = container.querySelector('#{{ widget.attrs.id }}_preview_img');
    const uploadArea = container.querySelector('.image-upload-area');
    
    previewImg.src = '{{ widget.value.url }}';
    preview.style.display = 'block';
    uploadArea.classList.add('has-image');
});
</script>
{% endif %}
