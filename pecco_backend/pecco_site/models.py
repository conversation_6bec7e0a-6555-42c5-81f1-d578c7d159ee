from django.db import models
from imagekit.models import ImageSpecField
from imagekit.processors import ResizeToFit
from .image_specs import *
from .fields import ProductImageField, CarouselImageField, CategoryImageField, VariantImageField

LOCALES = (
    ('zh', '中文'),
    ('en', 'English'),
    ('de', 'Deutsch'),
    ('nl', 'Nederlands'),
    ('fr', 'Français'),
)

class TimeStampedModel(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    class Meta:
        abstract = True

class Category(TimeStampedModel):
    slug = models.SlugField(unique=True)
    icon = CategoryImageField(upload_to='category_icons/', blank=True, null=True)

    # 自动生成的优化图片
    icon_small = ImageSpecField(source='icon', id='category_icon_small')
    icon_medium = ImageSpecField(source='icon', id='category_icon_medium')

    sort_order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = '产品分类'
        verbose_name_plural = '产品分类'
        ordering = ['sort_order', 'slug']

    def __str__(self):
        return self.slug

class CategoryTranslation(models.Model):
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='translations')
    locale = models.CharField(max_length=5, choices=LOCALES)
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, default='')
    class Meta:
        unique_together = ('category', 'locale')
    def __str__(self):
        return f"{self.category.slug} [{self.locale}]"

class Product(TimeStampedModel):
    USAGE_CHOICES = (('indoor','Indoor'),('outdoor','Outdoor'))
    categories = models.ManyToManyField(Category, related_name='products')
    cover_image = ProductImageField(upload_to='product_covers/')
    hover_image = ProductImageField(upload_to='product_covers/', blank=True, null=True, help_text='悬停时显示的第二张图片（可选）')

    # 自动生成的优化图片
    cover_thumbnail = ImageSpecField(source='cover_image', id='product_cover_thumbnail')
    cover_medium = ImageSpecField(source='cover_image', id='product_cover_medium')
    cover_large = ImageSpecField(source='cover_image', id='product_cover_large')
    hover_thumbnail = ImageSpecField(source='hover_image', id='product_cover_thumbnail')
    hover_medium = ImageSpecField(source='hover_image', id='product_cover_medium')
    hover_large = ImageSpecField(source='hover_image', id='product_cover_large')

    is_active = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=0)
    usage = models.CharField(max_length=20, choices=USAGE_CHOICES, default='indoor')
    tag_new = models.BooleanField(default=False)
    tag_hot = models.BooleanField(default=False)
    tag_featured = models.BooleanField(default=False)

    class Meta:
        verbose_name = '产品'
        verbose_name_plural = '产品'
        ordering = ['sort_order', '-created_at']

    def get_chinese_name(self):
        """获取中文名称"""
        trans = self.translations.filter(locale='zh').first()
        return trans.name if trans else f"Product {self.id}"

    def __str__(self):
        return self.get_chinese_name()



class ProductTranslation(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='translations')
    locale = models.CharField(max_length=5, choices=LOCALES)
    name = models.CharField(max_length=200)
    short_desc = models.CharField(max_length=300, blank=True, default='')
    rich_desc = models.TextField(blank=True, default='')

    # 新增：默认产品详情翻译
    default_product_details = models.TextField(blank=True, default='', verbose_name='默认产品详情',
                                             help_text='当变体没有自定义产品详情时显示的默认内容')

    # 新增：通用规格翻译
    general_specifications = models.TextField(blank=True, default='', verbose_name='通用规格',
                                            help_text='适用于所有变体的通用规格信息，JSON格式：{"Weight Range": "2-6 kg", "Material": "Premium Cotton"}')

    class Meta:
        unique_together = ('product', 'locale')

class CarouselItem(TimeStampedModel):
    image = CarouselImageField(upload_to='carousels/')
    link = models.CharField(max_length=500, blank=True, null=True, help_text='链接地址，可以是相对路径如 /products/ 或完整URL')

    # 自动生成的优化图片
    image_thumbnail = ImageSpecField(source='image', id='carousel_thumbnail')
    image_medium = ImageSpecField(source='image', id='carousel_medium')
    image_large = ImageSpecField(source='image', id='carousel_large')

    sort_order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = '轮播图'
        verbose_name_plural = '轮播图'
        ordering = ['sort_order']

class CarouselTranslation(models.Model):
    item = models.ForeignKey(CarouselItem, on_delete=models.CASCADE, related_name='translations')
    locale = models.CharField(max_length=5, choices=LOCALES, verbose_name='语言')
    title = models.CharField(max_length=200, blank=True, default='', verbose_name='标题')
    subtitle = models.CharField(max_length=300, blank=True, default='', verbose_name='副标题')
    cta_text = models.CharField(max_length=100, blank=True, default='', verbose_name='按钮文字')

    class Meta:
        unique_together = ('item', 'locale')
        verbose_name = '轮播图翻译'
        verbose_name_plural = '轮播图翻译'

    def __str__(self):
        return f"{self.title} [{self.locale}]"



class NavigationItem(TimeStampedModel):
    TYPE = (('link','链接'),('category','分类'),('page','页面'))
    label_key = models.SlugField(help_text='用于多语言映射的 key')
    type = models.CharField(max_length=20, choices=TYPE, default='link')
    target = models.CharField(max_length=255, help_text='URL 或引用标识')
    is_active = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=0)

    class Meta:
        verbose_name = '导航菜单'
        verbose_name_plural = '导航菜单'
        ordering = ['sort_order']

class NavigationTranslation(models.Model):
    navigation = models.ForeignKey(NavigationItem, on_delete=models.CASCADE, related_name='translations')
    locale = models.CharField(max_length=5, choices=LOCALES)
    label = models.CharField(max_length=100)
    class Meta:
        unique_together = ('navigation', 'locale')

class ContactMessage(TimeStampedModel):
    """客户联系留言"""
    MESSAGE_TYPES = (
        ('contact', '联系我们'),
        ('newsletter', '邮件订阅'),
        ('reseller', '经销商申请'),
    )

    message_type = models.CharField(max_length=20, choices=MESSAGE_TYPES, default='contact', verbose_name='消息类型')
    name = models.CharField(max_length=100, blank=True, default='', verbose_name='姓名')
    email = models.EmailField(verbose_name='邮箱')
    message = models.TextField(blank=True, default='', verbose_name='留言内容')
    is_read = models.BooleanField(default=False, verbose_name='已读')
    replied_at = models.DateTimeField(blank=True, null=True, verbose_name='回复时间')

    class Meta:
        verbose_name = '客户留言'
        verbose_name_plural = '客户留言'
        ordering = ['-created_at']

    def __str__(self):
        type_display = self.get_message_type_display()
        if self.name:
            return f'[{type_display}] {self.name} - {self.email} ({self.created_at.strftime("%Y-%m-%d %H:%M")})'
        else:
            return f'[{type_display}] {self.email} ({self.created_at.strftime("%Y-%m-%d %H:%M")})'

class HomeLayoutBlock(TimeStampedModel):
    BLOCK_TYPES = (
        ('carousel','轮播'),
        ('about_us','关于我们'),
        ('categories','分类'),
        ('products','产品'),
        ('reseller','成为经销商'),
        ('custom','自定义'),
    )
    block_type = models.CharField(max_length=30, choices=BLOCK_TYPES)
    is_active = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=0)
    config = models.JSONField(default=dict, blank=True)

    class Meta:
        verbose_name = '首页布局模块'
        verbose_name_plural = '首页布局模块'
        ordering = ['sort_order']


class HomeLayoutBlockTranslation(models.Model):
    block = models.ForeignKey(HomeLayoutBlock, on_delete=models.CASCADE, related_name='translations')
    locale = models.CharField(max_length=5, choices=LOCALES)
    title = models.CharField(max_length=200, blank=True, default='')
    content = models.TextField(blank=True, default='')
    class Meta:
        unique_together = ('block', 'locale')


class StaticPage(TimeStampedModel):
    """静态页面管理"""
    PAGE_TYPES = (
        ('about', '关于我们'),
        ('privacy', '隐私政策'),
        ('terms', '服务条款'),
        ('contact', '联系我们'),
        ('custom', '自定义页面'),
    )
    page_type = models.CharField(max_length=20, choices=PAGE_TYPES, unique=True, verbose_name='页面类型')
    is_active = models.BooleanField(default=True, verbose_name='是否激活')
    meta_title = models.CharField(max_length=200, blank=True, default='', verbose_name='页面标题')
    meta_description = models.TextField(blank=True, default='', verbose_name='页面描述')
    
    class Meta:
        verbose_name = '静态页面'
        verbose_name_plural = '静态页面'
        ordering = ['page_type']
    
    def __str__(self):
        return self.get_page_type_display()

class StaticPageTranslation(models.Model):
    """静态页面翻译"""
    page = models.ForeignKey(StaticPage, on_delete=models.CASCADE, related_name='translations', verbose_name='页面')
    locale = models.CharField(max_length=5, choices=LOCALES, verbose_name='语言')
    title = models.CharField(max_length=200, verbose_name='页面标题')
    content = models.TextField(verbose_name='页面内容')
    meta_title = models.CharField(max_length=200, blank=True, default='', verbose_name='SEO标题')
    meta_description = models.TextField(blank=True, default='', verbose_name='SEO描述')

    class Meta:
        unique_together = ('page', 'locale')
        verbose_name = '页面翻译'
        verbose_name_plural = '页面翻译'

    def __str__(self):
        return f"{self.page.get_page_type_display()} [{self.locale}]"


# Product Variant Models
class ProductOption(TimeStampedModel):
    """产品选项类型（如：规格、颜色）"""
    OPTION_TYPES = (
        ('size', '规格'),
        ('color', '颜色'),
        ('material', '材质'),
        ('other', '其他'),
    )

    name = models.CharField(max_length=50, verbose_name='选项名称')
    option_type = models.CharField(max_length=20, choices=OPTION_TYPES, verbose_name='选项类型')
    sort_order = models.PositiveIntegerField(default=0, verbose_name='排序')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')

    class Meta:
        verbose_name = '产品选项类型'
        verbose_name_plural = '产品选项类型'
        ordering = ['sort_order', 'name']

    def __str__(self):
        return f"{self.name} ({self.get_option_type_display()})"


class ProductOptionTranslation(models.Model):
    """产品选项类型翻译"""
    option = models.ForeignKey(ProductOption, on_delete=models.CASCADE, related_name='translations')
    locale = models.CharField(max_length=5, choices=LOCALES)
    name = models.CharField(max_length=50, verbose_name='选项名称')

    class Meta:
        unique_together = ('option', 'locale')
        verbose_name = '选项类型翻译'
        verbose_name_plural = '选项类型翻译'

    def __str__(self):
        return f"{self.option.name} [{self.locale}]"


class ProductOptionValue(TimeStampedModel):
    """产品选项值（如：60x50cm、红色）"""
    option = models.ForeignKey(ProductOption, on_delete=models.CASCADE, related_name='values')
    value = models.CharField(max_length=100, verbose_name='选项值')
    color_code = models.CharField(max_length=7, blank=True, null=True, verbose_name='颜色代码',
                                  help_text='颜色选项的十六进制颜色代码，如：#FF0000')
    sort_order = models.PositiveIntegerField(default=0, verbose_name='排序')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')

    class Meta:
        verbose_name = '产品选项值'
        verbose_name_plural = '产品选项值'
        ordering = ['option', 'sort_order', 'value']
        unique_together = ('option', 'value')

    def __str__(self):
        return f"{self.option.name}: {self.value}"


class ProductOptionValueTranslation(models.Model):
    """产品选项值翻译"""
    option_value = models.ForeignKey(ProductOptionValue, on_delete=models.CASCADE, related_name='translations')
    locale = models.CharField(max_length=5, choices=LOCALES)
    value = models.CharField(max_length=100, verbose_name='选项值')

    class Meta:
        unique_together = ('option_value', 'locale')
        verbose_name = '选项值翻译'
        verbose_name_plural = '选项值翻译'

    def __str__(self):
        return f"{self.option_value.value} [{self.locale}]"


class ProductVariant(TimeStampedModel):
    """产品变体（尺寸+颜色的组合）"""
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='variants')
    option_values = models.ManyToManyField(ProductOptionValue, verbose_name='选项值组合')
    sku = models.CharField(max_length=100, blank=True, null=True, verbose_name='SKU编码')
    product_details = models.TextField(blank=True, null=True, verbose_name='产品详情')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    sort_order = models.PositiveIntegerField(default=0, verbose_name='排序')

    class Meta:
        verbose_name = '产品变体'
        verbose_name_plural = '产品变体'
        ordering = ['product', 'sort_order']

    def __str__(self):
        values = " + ".join([str(v) for v in self.option_values.all()])
        return f"{self.product.get_chinese_name()} ({values})"

    def get_option_combination(self):
        """获取选项组合的字典形式"""
        combination = {}
        for value in self.option_values.all():
            combination[value.option.option_type] = value
        return combination


class ProductVariantImage(models.Model):
    """产品变体图片"""
    variant = models.ForeignKey(ProductVariant, on_delete=models.CASCADE, related_name='images')
    image = VariantImageField(upload_to='product_variants/', verbose_name='变体图片')

    # 自动生成的优化图片
    image_thumbnail = ImageSpecField(source='image', id='variant_image_thumbnail')
    image_medium = ImageSpecField(source='image', id='variant_image_medium')
    image_large = ImageSpecField(source='image', id='variant_image_large')

    sort_order = models.PositiveIntegerField(default=0, verbose_name='排序')
    is_primary = models.BooleanField(default=False, verbose_name='是否为主图')

    class Meta:
        verbose_name = '产品变体图片'
        verbose_name_plural = '产品变体图片'
        ordering = ['variant', 'sort_order']

    def __str__(self):
        return f"{self.variant} - 图片 {self.sort_order}"

    def save(self, *args, **kwargs):
        # 确保每个变体只有一张主图
        if self.is_primary:
            # 将同一变体的其他图片设为非主图
            ProductVariantImage.objects.filter(
                variant=self.variant,
                is_primary=True
            ).exclude(pk=self.pk).update(is_primary=False)

        super().save(*args, **kwargs)

        # 保存后验证：如果变体有图片，必须有且仅有一张主图
        self._validate_primary_image()

    def _validate_primary_image(self):
        """验证变体的主图设置"""
        variant_images = ProductVariantImage.objects.filter(variant=self.variant)
        if variant_images.exists():
            primary_count = variant_images.filter(is_primary=True).count()
            if primary_count == 0:
                # 如果没有主图，将第一张图片设为主图
                first_image = variant_images.first()
                first_image.is_primary = True
                first_image.save()
            elif primary_count > 1:
                # 如果有多张主图，只保留第一张
                primary_images = variant_images.filter(is_primary=True)
                first_primary = primary_images.first()
                primary_images.exclude(pk=first_primary.pk).update(is_primary=False)


class ProductSpecification(TimeStampedModel):
    """产品规格详情"""
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='specifications')
    variant = models.ForeignKey(ProductVariant, on_delete=models.CASCADE, related_name='specifications',
                               blank=True, null=True, verbose_name='关联变体')
    spec_key = models.CharField(max_length=100, verbose_name='规格键')
    spec_value = models.TextField(verbose_name='规格值')
    sort_order = models.PositiveIntegerField(default=0, verbose_name='排序')
    is_active = models.BooleanField(default=True, verbose_name='是否显示')

    class Meta:
        verbose_name = '产品规格'
        verbose_name_plural = '产品规格'
        ordering = ['product', 'variant', 'sort_order']

    def __str__(self):
        variant_info = f" ({self.variant})" if self.variant else ""
        return f"{self.product.get_chinese_name()}{variant_info} - {self.spec_key}"


class ProductSpecificationTranslation(models.Model):
    """产品规格翻译"""
    specification = models.ForeignKey(ProductSpecification, on_delete=models.CASCADE, related_name='translations')
    locale = models.CharField(max_length=5, choices=LOCALES)
    spec_key = models.CharField(max_length=100, verbose_name='规格键')
    spec_value = models.TextField(verbose_name='规格值')

    class Meta:
        unique_together = ('specification', 'locale')
        verbose_name = '规格翻译'
        verbose_name_plural = '规格翻译'

    def __str__(self):
        return f"{self.specification.spec_key} [{self.locale}]"

